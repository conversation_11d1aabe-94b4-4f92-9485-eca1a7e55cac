# 🎯 Multi-Timeframe Live Data Fetcher with WMA Crossover Detection

A powerful Python application for **real-time multi-timeframe data collection** with **WMA crossover signal detection** for NIFTY INDEX trading.

## 🚀 **Key Features**

- **📊 Multi-Timeframe Processing**: Simultaneous 1min, 5min, and 60min data collection
- **🔥 Real-time WMA Crossover Detection**: Live UP/DOWN signal alerts
- **📈 Enhanced CSV Output**: Historical and live data with WMA5_10 indicators
- **⚡ Parallel Processing**: Efficient multi-threaded data collection
- **🎯 Clean Signal Focus**: Pure crossover analysis without zone complexity
- **📅 Historical Data Management**: Automatic rolling window maintenance

## 📁 **Project Structure**

```
Current Directory (live-data-fetching)/
├── 📊 Data Folders
│   ├── Historical_Data_Layer/
│   │   ├── historical_1min.csv      # 3 days rolling window
│   │   ├── historical_5min.csv      # 7 days rolling window
│   │   └── historical_60min.csv     # 30 days rolling window
│   ├── Live_Data_Layer/
│   │   ├── NIFTY_INDEX_1min_live.csv
│   │   ├── NIFTY_INDEX_5min_live.csv
│   │   └── NIFTY_INDEX_60min_live.csv
│   ├── Dependencies/                 # Additional data files
│   ├── recentdata/                   # Recent data backups
│   ├── stock_data/                   # Stock-specific data
│   └── logs/                         # System logs
├── 🎯 Scripts/
│   ├── multi_timeframe_live_fetcher.py    # Main crossover detection script
│   ├── generate_crossover_indicators.py   # Utility for existing data
│   ├── DhanHQ_Enhanced_Live_Fetcher.py    # Legacy DhanHQ script
│   ├── live_data.py                       # Live data utilities
│   ├── live_dynamic_v2.py                 # Dynamic live fetcher
│   └── generate_zones_and_indicators.py   # Zone analysis (legacy)
├── 📖 Documentation/
│   ├── README.md                     # This main documentation
│   ├── SETUP_GUIDE.md               # Detailed setup instructions
│   ├── CROSSOVER_ONLY_SUMMARY.md    # Crossover feature summary
│   ├── verify_installation.py       # Installation verification
│   └── Other documentation files
├── 📦 Config/
│   ├── requirements.txt             # Full dependencies
│   ├── requirements-minimal.txt     # Essential dependencies only
│   ├── dhan_nse_eq.csv             # NSE equity securities
│   └── dhan_nse_idx.csv            # NSE index securities
└── 🔧 Other_Files/
    ├── ta-lib/                      # TA-Lib library files
    ├── __pycache__/                 # Python cache
    └── *.log                        # Log files
```

## 📋 **Prerequisites**

- **Python 3.8+** (recommended: Python 3.9+)
- **uv package manager** (recommended) or pip
- **DhanHQ API access** with valid credentials

## 🔧 **Quick Installation**

### **1. Install Dependencies**
```bash
# Using uv (recommended)
uv pip install -r Config/requirements-minimal.txt

# Using pip
pip install dhanhq pandas numpy
```

### **2. Configure API Credentials**
Edit `Scripts/multi_timeframe_live_fetcher.py`:
```python
CLIENT_ID = "YOUR_CLIENT_ID"
ACCESS_TOKEN = "YOUR_ACCESS_TOKEN"
```

### **3. Verify Installation**
```bash
python Documentation/verify_installation.py
```

## 🚀 **Usage**

### **Run Live Data Fetcher**
```bash
# Start live crossover detection
python Scripts/multi_timeframe_live_fetcher.py

# Or using uv
uv run python Scripts/multi_timeframe_live_fetcher.py
```

### **Generate Crossover Indicators for Existing Data**
```bash
python Scripts/generate_crossover_indicators.py
```

## 📊 **Output Data Format**

### **CSV Structure with Crossover Indicators**
```csv
open,high,low,close,volume,timestamp,wma5_10
24662.4,24705.5,24655.8,24705.5,0.0,2025-06-03 10:15:00,UP
24645.25,24650.35,24612.1,24642.85,0.0,2025-06-03 10:40:00,DOWN
24630.4,24640.25,24619.15,24639.5,0.0,2025-06-03 10:00:00,HOLD
```

### **Signal Types**
- **UP**: 5 WMA crosses above 10 WMA (Bullish signal)
- **DOWN**: 5 WMA crosses below 10 WMA (Bearish signal)
- **HOLD**: No clear crossover (Neutral state)

## 🚨 **Live Alerts**

```
[1min] 🚨 NEW LIVE CROSSOVER: UP at 2025-06-05 14:31:00
[1min] 📊 WMA5: 24755.23, WMA10: 24752.45
[5min] 🚨 NEW LIVE CROSSOVER: DOWN at 2025-06-05 14:35:00
[5min] 📊 WMA5: 24748.12, WMA10: 24751.67
```

## 📖 **Documentation**

- **📋 Setup Guide**: `Documentation/SETUP_GUIDE.md`
- **🎯 Feature Summary**: `Documentation/CROSSOVER_ONLY_SUMMARY.md`
- **🔧 Installation Check**: `Documentation/verify_installation.py`

## 🛠️ **Troubleshooting**

### **Common Issues**
1. **Import Errors**: Install dependencies with `uv pip install -r Config/requirements-minimal.txt`
2. **API Authentication**: Verify CLIENT_ID and ACCESS_TOKEN in the script
3. **No Data**: Check market hours (9:15 AM - 3:30 PM IST) and internet connection

### **Check Status**
```bash
# View logs
tail -f Other_Files/multi_timeframe_fetcher.log

# Check data files
ls -la Historical_Data_Layer/
ls -la Live_Data_Layer/
```

## ⚙️ **Configuration**

### **Timeframe Settings**
- **1min**: 3 days retention (1,125 candles)
- **5min**: 7 days retention (525 candles)
- **60min**: 30 days retention (180 candles)

### **Market Hours**
- **Open**: 9:15 AM IST
- **Close**: 3:30 PM IST
- **Days**: Monday-Friday (excluding holidays)

## 🎉 **Ready for Trading!**

Your **Multi-Timeframe Live Data Fetcher with WMA Crossover Detection** is now organized and ready for real-time trading signal analysis!

**Happy Trading! 📈🎯**
