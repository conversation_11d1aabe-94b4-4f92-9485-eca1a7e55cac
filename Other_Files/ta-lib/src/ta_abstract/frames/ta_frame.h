/* TA-LIB Copyright (c) 1999-2007, <PERSON>
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or
 * without modification, are permitted provided that the following
 * conditions are met:
 *
 * - Redistributions of source code must retain the above copyright
 *   notice, this list of conditions and the following disclaimer.
 *
 * - Redistributions in binary form must reproduce the above copyright
 *   notice, this list of conditions and the following disclaimer in
 *   the documentation and/or other materials provided with the
 *   distribution.
 *
 * - Neither name of author nor the names of its contributors
 *   may be used to endorse or promote products derived from this
 *   software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 * ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 * LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
 * FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
 * REGENTS OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
 * INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
 * OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE
 * OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

#ifndef TA_FRAME_H
#define TA_FRAME_H

/* Note: This file is generated by gencode, do not
 *       modify directly.
 */

/* The function define in this file allows to call a TA
 * function through the TA_CallFunc mechanism.
 */

#ifndef TA_COMMON_H
   #include "ta_common.h"
#endif

#ifndef TA_FRAME_PRIV_H
   #include "ta_frame_priv.h"
#endif


/* Generated */ TA_RetCode TA_ACOS_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_ACOS_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_AD_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_AD_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_ADD_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_ADD_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_ADOSC_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_ADOSC_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_ADX_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_ADX_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_ADXR_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_ADXR_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_APO_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_APO_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_AROON_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_AROON_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_AROONOSC_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_AROONOSC_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_ASIN_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_ASIN_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_ATAN_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_ATAN_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_ATR_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_ATR_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_AVGPRICE_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_AVGPRICE_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_BBANDS_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_BBANDS_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_BETA_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_BETA_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_BOP_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_BOP_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CCI_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CCI_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDL2CROWS_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDL2CROWS_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDL3BLACKCROWS_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDL3BLACKCROWS_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDL3INSIDE_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDL3INSIDE_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDL3LINESTRIKE_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDL3LINESTRIKE_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDL3OUTSIDE_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDL3OUTSIDE_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDL3STARSINSOUTH_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDL3STARSINSOUTH_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDL3WHITESOLDIERS_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDL3WHITESOLDIERS_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLABANDONEDBABY_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLABANDONEDBABY_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLADVANCEBLOCK_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLADVANCEBLOCK_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLBELTHOLD_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLBELTHOLD_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLBREAKAWAY_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLBREAKAWAY_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLCLOSINGMARUBOZU_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLCLOSINGMARUBOZU_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLCONCEALBABYSWALL_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLCONCEALBABYSWALL_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLCOUNTERATTACK_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLCOUNTERATTACK_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLDARKCLOUDCOVER_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLDARKCLOUDCOVER_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLDOJI_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLDOJI_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLDOJISTAR_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLDOJISTAR_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLDRAGONFLYDOJI_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLDRAGONFLYDOJI_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLENGULFING_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLENGULFING_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLEVENINGDOJISTAR_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLEVENINGDOJISTAR_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLEVENINGSTAR_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLEVENINGSTAR_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLGAPSIDESIDEWHITE_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLGAPSIDESIDEWHITE_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLGRAVESTONEDOJI_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLGRAVESTONEDOJI_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLHAMMER_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLHAMMER_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLHANGINGMAN_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLHANGINGMAN_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLHARAMI_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLHARAMI_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLHARAMICROSS_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLHARAMICROSS_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLHIGHWAVE_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLHIGHWAVE_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLHIKKAKE_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLHIKKAKE_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLHIKKAKEMOD_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLHIKKAKEMOD_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLHOMINGPIGEON_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLHOMINGPIGEON_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLIDENTICAL3CROWS_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLIDENTICAL3CROWS_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLINNECK_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLINNECK_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLINVERTEDHAMMER_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLINVERTEDHAMMER_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLKICKING_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLKICKING_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLKICKINGBYLENGTH_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLKICKINGBYLENGTH_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLLADDERBOTTOM_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLLADDERBOTTOM_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLLONGLEGGEDDOJI_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLLONGLEGGEDDOJI_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLLONGLINE_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLLONGLINE_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLMARUBOZU_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLMARUBOZU_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLMATCHINGLOW_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLMATCHINGLOW_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLMATHOLD_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLMATHOLD_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLMORNINGDOJISTAR_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLMORNINGDOJISTAR_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLMORNINGSTAR_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLMORNINGSTAR_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLONNECK_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLONNECK_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLPIERCING_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLPIERCING_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLRICKSHAWMAN_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLRICKSHAWMAN_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLRISEFALL3METHODS_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLRISEFALL3METHODS_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLSEPARATINGLINES_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLSEPARATINGLINES_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLSHOOTINGSTAR_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLSHOOTINGSTAR_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLSHORTLINE_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLSHORTLINE_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLSPINNINGTOP_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLSPINNINGTOP_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLSTALLEDPATTERN_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLSTALLEDPATTERN_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLSTICKSANDWICH_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLSTICKSANDWICH_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLTAKURI_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLTAKURI_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLTASUKIGAP_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLTASUKIGAP_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLTHRUSTING_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLTHRUSTING_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLTRISTAR_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLTRISTAR_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLUNIQUE3RIVER_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLUNIQUE3RIVER_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLUPSIDEGAP2CROWS_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLUPSIDEGAP2CROWS_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CDLXSIDEGAP3METHODS_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CDLXSIDEGAP3METHODS_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CEIL_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CEIL_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CMO_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CMO_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_CORREL_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_CORREL_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_COS_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_COS_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_COSH_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_COSH_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_DEMA_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_DEMA_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_DIV_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_DIV_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_DX_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_DX_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_EMA_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_EMA_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_EXP_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_EXP_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_FLOOR_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_FLOOR_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_HT_DCPERIOD_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_HT_DCPERIOD_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_HT_DCPHASE_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_HT_DCPHASE_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_HT_PHASOR_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_HT_PHASOR_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_HT_SINE_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_HT_SINE_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_HT_TRENDLINE_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_HT_TRENDLINE_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_HT_TRENDMODE_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_HT_TRENDMODE_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_KAMA_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_KAMA_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_LINEARREG_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_LINEARREG_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_LINEARREG_ANGLE_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_LINEARREG_ANGLE_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_LINEARREG_INTERCEPT_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_LINEARREG_INTERCEPT_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_LINEARREG_SLOPE_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_LINEARREG_SLOPE_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_LN_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_LN_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_LOG10_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_LOG10_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_MA_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_MA_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_MACD_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_MACD_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_MACDEXT_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_MACDEXT_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_MACDFIX_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_MACDFIX_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_MAMA_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_MAMA_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_MAVP_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_MAVP_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_MAX_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_MAX_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_MAXINDEX_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_MAXINDEX_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_MEDPRICE_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_MEDPRICE_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_MFI_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_MFI_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_MIDPOINT_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_MIDPOINT_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_MIDPRICE_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_MIDPRICE_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_MIN_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_MIN_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_MININDEX_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_MININDEX_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_MINMAX_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_MINMAX_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_MINMAXINDEX_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_MINMAXINDEX_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_MINUS_DI_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_MINUS_DI_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_MINUS_DM_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_MINUS_DM_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_MOM_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_MOM_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_MULT_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_MULT_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_NATR_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_NATR_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_OBV_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_OBV_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_PLUS_DI_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_PLUS_DI_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_PLUS_DM_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_PLUS_DM_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_PPO_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_PPO_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_ROC_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_ROC_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_ROCP_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_ROCP_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_ROCR_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_ROCR_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_ROCR100_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_ROCR100_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_RSI_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_RSI_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_SAR_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_SAR_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_SAREXT_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_SAREXT_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_SIN_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_SIN_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_SINH_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_SINH_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_SMA_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_SMA_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_SQRT_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_SQRT_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_STDDEV_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_STDDEV_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_STOCH_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_STOCH_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_STOCHF_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_STOCHF_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_STOCHRSI_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_STOCHRSI_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_SUB_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_SUB_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_SUM_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_SUM_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_T3_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_T3_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_TAN_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_TAN_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_TANH_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_TANH_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_TEMA_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_TEMA_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_TRANGE_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_TRANGE_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_TRIMA_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_TRIMA_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_TRIX_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_TRIX_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_TSF_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_TSF_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_TYPPRICE_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_TYPPRICE_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_ULTOSC_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_ULTOSC_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_VAR_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_VAR_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_WCLPRICE_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_WCLPRICE_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_WILLR_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_WILLR_FramePPLB( const TA_ParamHolderPriv *params )
;

/* Generated */ TA_RetCode TA_WMA_FramePP( const TA_ParamHolderPriv *params,
/* Generated */                           int            startIdx,
/* Generated */                           int            endIdx,
/* Generated */                           int           *outBegIdx,
/* Generated */                           int           *outNBElement )
;
/* Generated */ unsigned int TA_WMA_FramePPLB( const TA_ParamHolderPriv *params )
;


#endif

/***************/
/* End of File */
/***************/
