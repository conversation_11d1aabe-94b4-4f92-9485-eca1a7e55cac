# Makefile.in generated by automake 1.10 from Makefile.am.
# src/ta_abstract/Makefile.  Generated from Makefile.in by configure.

# Copyright (C) 1994, 1995, 1996, 1997, 1998, 1999, 2000, 2001, 2002,
# 2003, 2004, 2005, 2006  Free Software Foundation, Inc.
# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.





pkgdatadir = $(datadir)/ta-lib
pkglibdir = $(libdir)/ta-lib
pkgincludedir = $(includedir)/ta-lib
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = x86_64-unknown-linux-gnu
host_triplet = x86_64-unknown-linux-gnu
subdir = src/ta_abstract
DIST_COMMON = $(libta_abstract_HEADERS) $(srcdir)/Makefile.am \
	$(srcdir)/Makefile.in
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/configure.in
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/include/ta_config.h
CONFIG_CLEAN_FILES =
LTLIBRARIES = $(noinst_LTLIBRARIES)
libta_abstract_la_LIBADD =
am_libta_abstract_la_OBJECTS = libta_abstract_la-ta_group_idx.lo \
	libta_abstract_la-ta_def_ui.lo \
	libta_abstract_la-ta_abstract.lo \
	libta_abstract_la-ta_func_api.lo libta_abstract_la-ta_frame.lo \
	libta_abstract_la-table_a.lo libta_abstract_la-table_b.lo \
	libta_abstract_la-table_c.lo libta_abstract_la-table_d.lo \
	libta_abstract_la-table_e.lo libta_abstract_la-table_f.lo \
	libta_abstract_la-table_g.lo libta_abstract_la-table_h.lo \
	libta_abstract_la-table_i.lo libta_abstract_la-table_j.lo \
	libta_abstract_la-table_k.lo libta_abstract_la-table_l.lo \
	libta_abstract_la-table_m.lo libta_abstract_la-table_n.lo \
	libta_abstract_la-table_o.lo libta_abstract_la-table_p.lo \
	libta_abstract_la-table_q.lo libta_abstract_la-table_r.lo \
	libta_abstract_la-table_s.lo libta_abstract_la-table_t.lo \
	libta_abstract_la-table_u.lo libta_abstract_la-table_v.lo \
	libta_abstract_la-table_w.lo libta_abstract_la-table_x.lo \
	libta_abstract_la-table_y.lo libta_abstract_la-table_z.lo
libta_abstract_la_OBJECTS = $(am_libta_abstract_la_OBJECTS)
libta_abstract_la_LINK = $(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(libta_abstract_la_LDFLAGS) $(LDFLAGS) -o $@
libta_abstract_gc_la_LIBADD =
am__objects_1 = libta_abstract_gc_la-ta_group_idx.lo \
	libta_abstract_gc_la-ta_def_ui.lo \
	libta_abstract_gc_la-ta_abstract.lo \
	libta_abstract_gc_la-ta_func_api.lo \
	libta_abstract_gc_la-ta_frame.lo \
	libta_abstract_gc_la-table_a.lo \
	libta_abstract_gc_la-table_b.lo \
	libta_abstract_gc_la-table_c.lo \
	libta_abstract_gc_la-table_d.lo \
	libta_abstract_gc_la-table_e.lo \
	libta_abstract_gc_la-table_f.lo \
	libta_abstract_gc_la-table_g.lo \
	libta_abstract_gc_la-table_h.lo \
	libta_abstract_gc_la-table_i.lo \
	libta_abstract_gc_la-table_j.lo \
	libta_abstract_gc_la-table_k.lo \
	libta_abstract_gc_la-table_l.lo \
	libta_abstract_gc_la-table_m.lo \
	libta_abstract_gc_la-table_n.lo \
	libta_abstract_gc_la-table_o.lo \
	libta_abstract_gc_la-table_p.lo \
	libta_abstract_gc_la-table_q.lo \
	libta_abstract_gc_la-table_r.lo \
	libta_abstract_gc_la-table_s.lo \
	libta_abstract_gc_la-table_t.lo \
	libta_abstract_gc_la-table_u.lo \
	libta_abstract_gc_la-table_v.lo \
	libta_abstract_gc_la-table_w.lo \
	libta_abstract_gc_la-table_x.lo \
	libta_abstract_gc_la-table_y.lo \
	libta_abstract_gc_la-table_z.lo
am_libta_abstract_gc_la_OBJECTS = $(am__objects_1)
libta_abstract_gc_la_OBJECTS = $(am_libta_abstract_gc_la_OBJECTS)
libta_abstract_gc_la_LINK = $(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(libta_abstract_gc_la_LDFLAGS) $(LDFLAGS) -o $@
DEFAULT_INCLUDES = -I. -I$(top_builddir)/include
depcomp = $(SHELL) $(top_srcdir)/depcomp
am__depfiles_maybe = depfiles
COMPILE = $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) \
	$(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
LTCOMPILE = $(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) \
	--mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) \
	$(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
CCLD = $(CC)
LINK = $(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) \
	--mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) $(AM_LDFLAGS) \
	$(LDFLAGS) -o $@
SOURCES = $(libta_abstract_la_SOURCES) $(libta_abstract_gc_la_SOURCES)
DIST_SOURCES = $(libta_abstract_la_SOURCES) \
	$(libta_abstract_gc_la_SOURCES)
am__vpath_adj_setup = srcdirstrip=`echo "$(srcdir)" | sed 's|.|.|g'`;
am__vpath_adj = case $$p in \
    $(srcdir)/*) f=`echo "$$p" | sed "s|^$$srcdirstrip/||"`;; \
    *) f=$$p;; \
  esac;
am__strip_dir = `echo $$p | sed -e 's|^.*/||'`;
am__installdirs = "$(DESTDIR)$(libta_abstractdir)"
libta_abstractHEADERS_INSTALL = $(INSTALL_HEADER)
HEADERS = $(libta_abstract_HEADERS)
ETAGS = etags
CTAGS = ctags
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
ACLOCAL = ${SHELL} /home/<USER>/Algo-trade/live-data-fetching/ta-lib/missing --run aclocal-1.10
AMTAR = ${SHELL} /home/<USER>/Algo-trade/live-data-fetching/ta-lib/missing --run tar
AR = ar
AUTOCONF = ${SHELL} /home/<USER>/Algo-trade/live-data-fetching/ta-lib/missing --run autoconf
AUTOHEADER = ${SHELL} /home/<USER>/Algo-trade/live-data-fetching/ta-lib/missing --run autoheader
AUTOMAKE = ${SHELL} /home/<USER>/Algo-trade/live-data-fetching/ta-lib/missing --run automake-1.10
AWK = mawk
CC = gcc
CCDEPMODE = depmode=gcc3
CFLAGS = -g -O2
CPP = gcc -E
CPPFLAGS = 
CXX = g++
CXXCPP = g++ -E
CXXDEPMODE = depmode=gcc3
CXXFLAGS = -g -O2
CYGPATH_W = echo
DEFS = -DHAVE_CONFIG_H
DEPDIR = .deps
ECHO = echo
ECHO_C = 
ECHO_N = -n
ECHO_T = 
EGREP = /usr/bin/grep -E
EXEEXT = 
F77 = 
FFLAGS = 
GREP = /usr/bin/grep
INSTALL = /usr/bin/install -c
INSTALL_DATA = ${INSTALL} -m 644
INSTALL_PROGRAM = ${INSTALL}
INSTALL_SCRIPT = ${INSTALL}
INSTALL_STRIP_PROGRAM = $(install_sh) -c -s
LDFLAGS = 
LIBOBJS = 
LIBS = -lpthread -ldl 
LIBTOOL = $(SHELL) $(top_builddir)/libtool
LN_S = ln -s
LTLIBOBJS = 
MAKEINFO = ${SHELL} /home/<USER>/Algo-trade/live-data-fetching/ta-lib/missing --run makeinfo
MKDIR_P = /usr/bin/mkdir -p
OBJEXT = o
PACKAGE = ta-lib
PACKAGE_BUGREPORT = http://sourceforge.net/tracker/?group_id=8903&atid=108903
PACKAGE_NAME = ta-lib
PACKAGE_STRING = ta-lib 0.4.0
PACKAGE_TARNAME = ta-lib
PACKAGE_VERSION = 0.4.0
PATH_SEPARATOR = :
POW_LIB = 
RANLIB = ranlib
SET_MAKE = 
SHELL = /bin/bash
STRIP = strip
TALIB_LIBRARY_VERSION = 0:0:0
VERSION = 0.4.0
abs_builddir = /home/<USER>/Algo-trade/live-data-fetching/ta-lib/src/ta_abstract
abs_srcdir = /home/<USER>/Algo-trade/live-data-fetching/ta-lib/src/ta_abstract
abs_top_builddir = /home/<USER>/Algo-trade/live-data-fetching/ta-lib
abs_top_srcdir = /home/<USER>/Algo-trade/live-data-fetching/ta-lib
ac_ct_CC = gcc
ac_ct_CXX = g++
ac_ct_F77 = 
am__include = include
am__leading_dot = .
am__quote = 
am__tar = ${AMTAR} chof - "$$tardir"
am__untar = ${AMTAR} xf -
bindir = ${exec_prefix}/bin
build = x86_64-unknown-linux-gnu
build_alias = 
build_cpu = x86_64
build_os = linux-gnu
build_vendor = unknown
builddir = .
datadir = ${datarootdir}
datarootdir = ${prefix}/share
docdir = ${datarootdir}/doc/${PACKAGE_TARNAME}
dvidir = ${docdir}
exec_prefix = ${prefix}
host = x86_64-unknown-linux-gnu
host_alias = 
host_cpu = x86_64
host_os = linux-gnu
host_vendor = unknown
htmldir = ${docdir}
includedir = ${prefix}/include
infodir = ${datarootdir}/info
install_sh = $(SHELL) /home/<USER>/Algo-trade/live-data-fetching/ta-lib/install-sh
libdir = ${exec_prefix}/lib
libexecdir = ${exec_prefix}/libexec
localedir = ${datarootdir}/locale
localstatedir = ${prefix}/var
mandir = ${datarootdir}/man
mkdir_p = /usr/bin/mkdir -p
oldincludedir = /usr/include
pdfdir = ${docdir}
prefix = /usr/local
program_transform_name = s,x,x,
psdir = ${docdir}
sbindir = ${exec_prefix}/sbin
sharedstatedir = ${prefix}/com
srcdir = .
sysconfdir = ${prefix}/etc
target_alias = 
top_builddir = ../..
top_srcdir = ../..
noinst_LTLIBRARIES = libta_abstract.la libta_abstract_gc.la
libta_abstract_la_SOURCES = ta_group_idx.c \
	ta_def_ui.c \
	ta_abstract.c \
	ta_func_api.c \
	frames/ta_frame.c \
	tables/table_a.c \
	tables/table_b.c \
	tables/table_c.c \
	tables/table_d.c \
	tables/table_e.c \
	tables/table_f.c \
	tables/table_g.c \
	tables/table_h.c \
	tables/table_i.c \
	tables/table_j.c \
	tables/table_k.c \
	tables/table_l.c \
	tables/table_m.c \
	tables/table_n.c \
	tables/table_o.c \
	tables/table_p.c \
	tables/table_q.c \
	tables/table_r.c \
	tables/table_s.c \
	tables/table_t.c \
	tables/table_u.c \
	tables/table_v.c \
	tables/table_w.c \
	tables/table_x.c \
	tables/table_y.c \
	tables/table_z.c

libta_abstract_gc_la_SOURCES = $(libta_abstract_la_SOURCES)
libta_abstract_la_LDFLAGS = -version-info $(TALIB_LIBRARY_VERSION)
libta_abstract_gc_la_LDFLAGS = $(libta_abstract_la_LDFLAGS)
libta_abstract_la_CPPFLAGS = -I../ta_common/ -Iframes/

# The 'gc' version is a minimal version used to just to compile gen_code
libta_abstract_gc_la_CPPFLAGS = -DTA_GEN_CODE $(libta_abstract_la_CPPFLAGS)
libta_abstractdir = $(includedir)/ta-lib/
libta_abstract_HEADERS = ../../include/ta_defs.h \
	../../include/ta_libc.h \
	../../include/ta_abstract.h

all: all-am

.SUFFIXES:
.SUFFIXES: .c .lo .o .obj
$(srcdir)/Makefile.in:  $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh \
		&& exit 0; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --gnu  src/ta_abstract/Makefile'; \
	cd $(top_srcdir) && \
	  $(AUTOMAKE) --gnu  src/ta_abstract/Makefile
.PRECIOUS: Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__depfiles_maybe)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__depfiles_maybe);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure:  $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4):  $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

clean-noinstLTLIBRARIES:
	-test -z "$(noinst_LTLIBRARIES)" || rm -f $(noinst_LTLIBRARIES)
	@list='$(noinst_LTLIBRARIES)'; for p in $$list; do \
	  dir="`echo $$p | sed -e 's|/[^/]*$$||'`"; \
	  test "$$dir" != "$$p" || dir=.; \
	  echo "rm -f \"$${dir}/so_locations\""; \
	  rm -f "$${dir}/so_locations"; \
	done
libta_abstract.la: $(libta_abstract_la_OBJECTS) $(libta_abstract_la_DEPENDENCIES) 
	$(libta_abstract_la_LINK)  $(libta_abstract_la_OBJECTS) $(libta_abstract_la_LIBADD) $(LIBS)
libta_abstract_gc.la: $(libta_abstract_gc_la_OBJECTS) $(libta_abstract_gc_la_DEPENDENCIES) 
	$(libta_abstract_gc_la_LINK)  $(libta_abstract_gc_la_OBJECTS) $(libta_abstract_gc_la_LIBADD) $(LIBS)

mostlyclean-compile:
	-rm -f *.$(OBJEXT)

distclean-compile:
	-rm -f *.tab.c

include ./$(DEPDIR)/libta_abstract_gc_la-ta_abstract.Plo
include ./$(DEPDIR)/libta_abstract_gc_la-ta_def_ui.Plo
include ./$(DEPDIR)/libta_abstract_gc_la-ta_frame.Plo
include ./$(DEPDIR)/libta_abstract_gc_la-ta_func_api.Plo
include ./$(DEPDIR)/libta_abstract_gc_la-ta_group_idx.Plo
include ./$(DEPDIR)/libta_abstract_gc_la-table_a.Plo
include ./$(DEPDIR)/libta_abstract_gc_la-table_b.Plo
include ./$(DEPDIR)/libta_abstract_gc_la-table_c.Plo
include ./$(DEPDIR)/libta_abstract_gc_la-table_d.Plo
include ./$(DEPDIR)/libta_abstract_gc_la-table_e.Plo
include ./$(DEPDIR)/libta_abstract_gc_la-table_f.Plo
include ./$(DEPDIR)/libta_abstract_gc_la-table_g.Plo
include ./$(DEPDIR)/libta_abstract_gc_la-table_h.Plo
include ./$(DEPDIR)/libta_abstract_gc_la-table_i.Plo
include ./$(DEPDIR)/libta_abstract_gc_la-table_j.Plo
include ./$(DEPDIR)/libta_abstract_gc_la-table_k.Plo
include ./$(DEPDIR)/libta_abstract_gc_la-table_l.Plo
include ./$(DEPDIR)/libta_abstract_gc_la-table_m.Plo
include ./$(DEPDIR)/libta_abstract_gc_la-table_n.Plo
include ./$(DEPDIR)/libta_abstract_gc_la-table_o.Plo
include ./$(DEPDIR)/libta_abstract_gc_la-table_p.Plo
include ./$(DEPDIR)/libta_abstract_gc_la-table_q.Plo
include ./$(DEPDIR)/libta_abstract_gc_la-table_r.Plo
include ./$(DEPDIR)/libta_abstract_gc_la-table_s.Plo
include ./$(DEPDIR)/libta_abstract_gc_la-table_t.Plo
include ./$(DEPDIR)/libta_abstract_gc_la-table_u.Plo
include ./$(DEPDIR)/libta_abstract_gc_la-table_v.Plo
include ./$(DEPDIR)/libta_abstract_gc_la-table_w.Plo
include ./$(DEPDIR)/libta_abstract_gc_la-table_x.Plo
include ./$(DEPDIR)/libta_abstract_gc_la-table_y.Plo
include ./$(DEPDIR)/libta_abstract_gc_la-table_z.Plo
include ./$(DEPDIR)/libta_abstract_la-ta_abstract.Plo
include ./$(DEPDIR)/libta_abstract_la-ta_def_ui.Plo
include ./$(DEPDIR)/libta_abstract_la-ta_frame.Plo
include ./$(DEPDIR)/libta_abstract_la-ta_func_api.Plo
include ./$(DEPDIR)/libta_abstract_la-ta_group_idx.Plo
include ./$(DEPDIR)/libta_abstract_la-table_a.Plo
include ./$(DEPDIR)/libta_abstract_la-table_b.Plo
include ./$(DEPDIR)/libta_abstract_la-table_c.Plo
include ./$(DEPDIR)/libta_abstract_la-table_d.Plo
include ./$(DEPDIR)/libta_abstract_la-table_e.Plo
include ./$(DEPDIR)/libta_abstract_la-table_f.Plo
include ./$(DEPDIR)/libta_abstract_la-table_g.Plo
include ./$(DEPDIR)/libta_abstract_la-table_h.Plo
include ./$(DEPDIR)/libta_abstract_la-table_i.Plo
include ./$(DEPDIR)/libta_abstract_la-table_j.Plo
include ./$(DEPDIR)/libta_abstract_la-table_k.Plo
include ./$(DEPDIR)/libta_abstract_la-table_l.Plo
include ./$(DEPDIR)/libta_abstract_la-table_m.Plo
include ./$(DEPDIR)/libta_abstract_la-table_n.Plo
include ./$(DEPDIR)/libta_abstract_la-table_o.Plo
include ./$(DEPDIR)/libta_abstract_la-table_p.Plo
include ./$(DEPDIR)/libta_abstract_la-table_q.Plo
include ./$(DEPDIR)/libta_abstract_la-table_r.Plo
include ./$(DEPDIR)/libta_abstract_la-table_s.Plo
include ./$(DEPDIR)/libta_abstract_la-table_t.Plo
include ./$(DEPDIR)/libta_abstract_la-table_u.Plo
include ./$(DEPDIR)/libta_abstract_la-table_v.Plo
include ./$(DEPDIR)/libta_abstract_la-table_w.Plo
include ./$(DEPDIR)/libta_abstract_la-table_x.Plo
include ./$(DEPDIR)/libta_abstract_la-table_y.Plo
include ./$(DEPDIR)/libta_abstract_la-table_z.Plo

.c.o:
	$(COMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ $<
	mv -f $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Po
#	source='$<' object='$@' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(COMPILE) -c $<

.c.obj:
	$(COMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ `$(CYGPATH_W) '$<'`
	mv -f $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Po
#	source='$<' object='$@' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(COMPILE) -c `$(CYGPATH_W) '$<'`

.c.lo:
	$(LTCOMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ $<
	mv -f $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Plo
#	source='$<' object='$@' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LTCOMPILE) -c -o $@ $<

libta_abstract_la-ta_group_idx.lo: ta_group_idx.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_la-ta_group_idx.lo -MD -MP -MF $(DEPDIR)/libta_abstract_la-ta_group_idx.Tpo -c -o libta_abstract_la-ta_group_idx.lo `test -f 'ta_group_idx.c' || echo '$(srcdir)/'`ta_group_idx.c
	mv -f $(DEPDIR)/libta_abstract_la-ta_group_idx.Tpo $(DEPDIR)/libta_abstract_la-ta_group_idx.Plo
#	source='ta_group_idx.c' object='libta_abstract_la-ta_group_idx.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_la-ta_group_idx.lo `test -f 'ta_group_idx.c' || echo '$(srcdir)/'`ta_group_idx.c

libta_abstract_la-ta_def_ui.lo: ta_def_ui.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_la-ta_def_ui.lo -MD -MP -MF $(DEPDIR)/libta_abstract_la-ta_def_ui.Tpo -c -o libta_abstract_la-ta_def_ui.lo `test -f 'ta_def_ui.c' || echo '$(srcdir)/'`ta_def_ui.c
	mv -f $(DEPDIR)/libta_abstract_la-ta_def_ui.Tpo $(DEPDIR)/libta_abstract_la-ta_def_ui.Plo
#	source='ta_def_ui.c' object='libta_abstract_la-ta_def_ui.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_la-ta_def_ui.lo `test -f 'ta_def_ui.c' || echo '$(srcdir)/'`ta_def_ui.c

libta_abstract_la-ta_abstract.lo: ta_abstract.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_la-ta_abstract.lo -MD -MP -MF $(DEPDIR)/libta_abstract_la-ta_abstract.Tpo -c -o libta_abstract_la-ta_abstract.lo `test -f 'ta_abstract.c' || echo '$(srcdir)/'`ta_abstract.c
	mv -f $(DEPDIR)/libta_abstract_la-ta_abstract.Tpo $(DEPDIR)/libta_abstract_la-ta_abstract.Plo
#	source='ta_abstract.c' object='libta_abstract_la-ta_abstract.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_la-ta_abstract.lo `test -f 'ta_abstract.c' || echo '$(srcdir)/'`ta_abstract.c

libta_abstract_la-ta_func_api.lo: ta_func_api.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_la-ta_func_api.lo -MD -MP -MF $(DEPDIR)/libta_abstract_la-ta_func_api.Tpo -c -o libta_abstract_la-ta_func_api.lo `test -f 'ta_func_api.c' || echo '$(srcdir)/'`ta_func_api.c
	mv -f $(DEPDIR)/libta_abstract_la-ta_func_api.Tpo $(DEPDIR)/libta_abstract_la-ta_func_api.Plo
#	source='ta_func_api.c' object='libta_abstract_la-ta_func_api.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_la-ta_func_api.lo `test -f 'ta_func_api.c' || echo '$(srcdir)/'`ta_func_api.c

libta_abstract_la-ta_frame.lo: frames/ta_frame.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_la-ta_frame.lo -MD -MP -MF $(DEPDIR)/libta_abstract_la-ta_frame.Tpo -c -o libta_abstract_la-ta_frame.lo `test -f 'frames/ta_frame.c' || echo '$(srcdir)/'`frames/ta_frame.c
	mv -f $(DEPDIR)/libta_abstract_la-ta_frame.Tpo $(DEPDIR)/libta_abstract_la-ta_frame.Plo
#	source='frames/ta_frame.c' object='libta_abstract_la-ta_frame.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_la-ta_frame.lo `test -f 'frames/ta_frame.c' || echo '$(srcdir)/'`frames/ta_frame.c

libta_abstract_la-table_a.lo: tables/table_a.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_la-table_a.lo -MD -MP -MF $(DEPDIR)/libta_abstract_la-table_a.Tpo -c -o libta_abstract_la-table_a.lo `test -f 'tables/table_a.c' || echo '$(srcdir)/'`tables/table_a.c
	mv -f $(DEPDIR)/libta_abstract_la-table_a.Tpo $(DEPDIR)/libta_abstract_la-table_a.Plo
#	source='tables/table_a.c' object='libta_abstract_la-table_a.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_la-table_a.lo `test -f 'tables/table_a.c' || echo '$(srcdir)/'`tables/table_a.c

libta_abstract_la-table_b.lo: tables/table_b.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_la-table_b.lo -MD -MP -MF $(DEPDIR)/libta_abstract_la-table_b.Tpo -c -o libta_abstract_la-table_b.lo `test -f 'tables/table_b.c' || echo '$(srcdir)/'`tables/table_b.c
	mv -f $(DEPDIR)/libta_abstract_la-table_b.Tpo $(DEPDIR)/libta_abstract_la-table_b.Plo
#	source='tables/table_b.c' object='libta_abstract_la-table_b.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_la-table_b.lo `test -f 'tables/table_b.c' || echo '$(srcdir)/'`tables/table_b.c

libta_abstract_la-table_c.lo: tables/table_c.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_la-table_c.lo -MD -MP -MF $(DEPDIR)/libta_abstract_la-table_c.Tpo -c -o libta_abstract_la-table_c.lo `test -f 'tables/table_c.c' || echo '$(srcdir)/'`tables/table_c.c
	mv -f $(DEPDIR)/libta_abstract_la-table_c.Tpo $(DEPDIR)/libta_abstract_la-table_c.Plo
#	source='tables/table_c.c' object='libta_abstract_la-table_c.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_la-table_c.lo `test -f 'tables/table_c.c' || echo '$(srcdir)/'`tables/table_c.c

libta_abstract_la-table_d.lo: tables/table_d.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_la-table_d.lo -MD -MP -MF $(DEPDIR)/libta_abstract_la-table_d.Tpo -c -o libta_abstract_la-table_d.lo `test -f 'tables/table_d.c' || echo '$(srcdir)/'`tables/table_d.c
	mv -f $(DEPDIR)/libta_abstract_la-table_d.Tpo $(DEPDIR)/libta_abstract_la-table_d.Plo
#	source='tables/table_d.c' object='libta_abstract_la-table_d.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_la-table_d.lo `test -f 'tables/table_d.c' || echo '$(srcdir)/'`tables/table_d.c

libta_abstract_la-table_e.lo: tables/table_e.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_la-table_e.lo -MD -MP -MF $(DEPDIR)/libta_abstract_la-table_e.Tpo -c -o libta_abstract_la-table_e.lo `test -f 'tables/table_e.c' || echo '$(srcdir)/'`tables/table_e.c
	mv -f $(DEPDIR)/libta_abstract_la-table_e.Tpo $(DEPDIR)/libta_abstract_la-table_e.Plo
#	source='tables/table_e.c' object='libta_abstract_la-table_e.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_la-table_e.lo `test -f 'tables/table_e.c' || echo '$(srcdir)/'`tables/table_e.c

libta_abstract_la-table_f.lo: tables/table_f.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_la-table_f.lo -MD -MP -MF $(DEPDIR)/libta_abstract_la-table_f.Tpo -c -o libta_abstract_la-table_f.lo `test -f 'tables/table_f.c' || echo '$(srcdir)/'`tables/table_f.c
	mv -f $(DEPDIR)/libta_abstract_la-table_f.Tpo $(DEPDIR)/libta_abstract_la-table_f.Plo
#	source='tables/table_f.c' object='libta_abstract_la-table_f.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_la-table_f.lo `test -f 'tables/table_f.c' || echo '$(srcdir)/'`tables/table_f.c

libta_abstract_la-table_g.lo: tables/table_g.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_la-table_g.lo -MD -MP -MF $(DEPDIR)/libta_abstract_la-table_g.Tpo -c -o libta_abstract_la-table_g.lo `test -f 'tables/table_g.c' || echo '$(srcdir)/'`tables/table_g.c
	mv -f $(DEPDIR)/libta_abstract_la-table_g.Tpo $(DEPDIR)/libta_abstract_la-table_g.Plo
#	source='tables/table_g.c' object='libta_abstract_la-table_g.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_la-table_g.lo `test -f 'tables/table_g.c' || echo '$(srcdir)/'`tables/table_g.c

libta_abstract_la-table_h.lo: tables/table_h.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_la-table_h.lo -MD -MP -MF $(DEPDIR)/libta_abstract_la-table_h.Tpo -c -o libta_abstract_la-table_h.lo `test -f 'tables/table_h.c' || echo '$(srcdir)/'`tables/table_h.c
	mv -f $(DEPDIR)/libta_abstract_la-table_h.Tpo $(DEPDIR)/libta_abstract_la-table_h.Plo
#	source='tables/table_h.c' object='libta_abstract_la-table_h.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_la-table_h.lo `test -f 'tables/table_h.c' || echo '$(srcdir)/'`tables/table_h.c

libta_abstract_la-table_i.lo: tables/table_i.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_la-table_i.lo -MD -MP -MF $(DEPDIR)/libta_abstract_la-table_i.Tpo -c -o libta_abstract_la-table_i.lo `test -f 'tables/table_i.c' || echo '$(srcdir)/'`tables/table_i.c
	mv -f $(DEPDIR)/libta_abstract_la-table_i.Tpo $(DEPDIR)/libta_abstract_la-table_i.Plo
#	source='tables/table_i.c' object='libta_abstract_la-table_i.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_la-table_i.lo `test -f 'tables/table_i.c' || echo '$(srcdir)/'`tables/table_i.c

libta_abstract_la-table_j.lo: tables/table_j.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_la-table_j.lo -MD -MP -MF $(DEPDIR)/libta_abstract_la-table_j.Tpo -c -o libta_abstract_la-table_j.lo `test -f 'tables/table_j.c' || echo '$(srcdir)/'`tables/table_j.c
	mv -f $(DEPDIR)/libta_abstract_la-table_j.Tpo $(DEPDIR)/libta_abstract_la-table_j.Plo
#	source='tables/table_j.c' object='libta_abstract_la-table_j.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_la-table_j.lo `test -f 'tables/table_j.c' || echo '$(srcdir)/'`tables/table_j.c

libta_abstract_la-table_k.lo: tables/table_k.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_la-table_k.lo -MD -MP -MF $(DEPDIR)/libta_abstract_la-table_k.Tpo -c -o libta_abstract_la-table_k.lo `test -f 'tables/table_k.c' || echo '$(srcdir)/'`tables/table_k.c
	mv -f $(DEPDIR)/libta_abstract_la-table_k.Tpo $(DEPDIR)/libta_abstract_la-table_k.Plo
#	source='tables/table_k.c' object='libta_abstract_la-table_k.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_la-table_k.lo `test -f 'tables/table_k.c' || echo '$(srcdir)/'`tables/table_k.c

libta_abstract_la-table_l.lo: tables/table_l.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_la-table_l.lo -MD -MP -MF $(DEPDIR)/libta_abstract_la-table_l.Tpo -c -o libta_abstract_la-table_l.lo `test -f 'tables/table_l.c' || echo '$(srcdir)/'`tables/table_l.c
	mv -f $(DEPDIR)/libta_abstract_la-table_l.Tpo $(DEPDIR)/libta_abstract_la-table_l.Plo
#	source='tables/table_l.c' object='libta_abstract_la-table_l.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_la-table_l.lo `test -f 'tables/table_l.c' || echo '$(srcdir)/'`tables/table_l.c

libta_abstract_la-table_m.lo: tables/table_m.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_la-table_m.lo -MD -MP -MF $(DEPDIR)/libta_abstract_la-table_m.Tpo -c -o libta_abstract_la-table_m.lo `test -f 'tables/table_m.c' || echo '$(srcdir)/'`tables/table_m.c
	mv -f $(DEPDIR)/libta_abstract_la-table_m.Tpo $(DEPDIR)/libta_abstract_la-table_m.Plo
#	source='tables/table_m.c' object='libta_abstract_la-table_m.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_la-table_m.lo `test -f 'tables/table_m.c' || echo '$(srcdir)/'`tables/table_m.c

libta_abstract_la-table_n.lo: tables/table_n.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_la-table_n.lo -MD -MP -MF $(DEPDIR)/libta_abstract_la-table_n.Tpo -c -o libta_abstract_la-table_n.lo `test -f 'tables/table_n.c' || echo '$(srcdir)/'`tables/table_n.c
	mv -f $(DEPDIR)/libta_abstract_la-table_n.Tpo $(DEPDIR)/libta_abstract_la-table_n.Plo
#	source='tables/table_n.c' object='libta_abstract_la-table_n.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_la-table_n.lo `test -f 'tables/table_n.c' || echo '$(srcdir)/'`tables/table_n.c

libta_abstract_la-table_o.lo: tables/table_o.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_la-table_o.lo -MD -MP -MF $(DEPDIR)/libta_abstract_la-table_o.Tpo -c -o libta_abstract_la-table_o.lo `test -f 'tables/table_o.c' || echo '$(srcdir)/'`tables/table_o.c
	mv -f $(DEPDIR)/libta_abstract_la-table_o.Tpo $(DEPDIR)/libta_abstract_la-table_o.Plo
#	source='tables/table_o.c' object='libta_abstract_la-table_o.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_la-table_o.lo `test -f 'tables/table_o.c' || echo '$(srcdir)/'`tables/table_o.c

libta_abstract_la-table_p.lo: tables/table_p.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_la-table_p.lo -MD -MP -MF $(DEPDIR)/libta_abstract_la-table_p.Tpo -c -o libta_abstract_la-table_p.lo `test -f 'tables/table_p.c' || echo '$(srcdir)/'`tables/table_p.c
	mv -f $(DEPDIR)/libta_abstract_la-table_p.Tpo $(DEPDIR)/libta_abstract_la-table_p.Plo
#	source='tables/table_p.c' object='libta_abstract_la-table_p.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_la-table_p.lo `test -f 'tables/table_p.c' || echo '$(srcdir)/'`tables/table_p.c

libta_abstract_la-table_q.lo: tables/table_q.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_la-table_q.lo -MD -MP -MF $(DEPDIR)/libta_abstract_la-table_q.Tpo -c -o libta_abstract_la-table_q.lo `test -f 'tables/table_q.c' || echo '$(srcdir)/'`tables/table_q.c
	mv -f $(DEPDIR)/libta_abstract_la-table_q.Tpo $(DEPDIR)/libta_abstract_la-table_q.Plo
#	source='tables/table_q.c' object='libta_abstract_la-table_q.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_la-table_q.lo `test -f 'tables/table_q.c' || echo '$(srcdir)/'`tables/table_q.c

libta_abstract_la-table_r.lo: tables/table_r.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_la-table_r.lo -MD -MP -MF $(DEPDIR)/libta_abstract_la-table_r.Tpo -c -o libta_abstract_la-table_r.lo `test -f 'tables/table_r.c' || echo '$(srcdir)/'`tables/table_r.c
	mv -f $(DEPDIR)/libta_abstract_la-table_r.Tpo $(DEPDIR)/libta_abstract_la-table_r.Plo
#	source='tables/table_r.c' object='libta_abstract_la-table_r.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_la-table_r.lo `test -f 'tables/table_r.c' || echo '$(srcdir)/'`tables/table_r.c

libta_abstract_la-table_s.lo: tables/table_s.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_la-table_s.lo -MD -MP -MF $(DEPDIR)/libta_abstract_la-table_s.Tpo -c -o libta_abstract_la-table_s.lo `test -f 'tables/table_s.c' || echo '$(srcdir)/'`tables/table_s.c
	mv -f $(DEPDIR)/libta_abstract_la-table_s.Tpo $(DEPDIR)/libta_abstract_la-table_s.Plo
#	source='tables/table_s.c' object='libta_abstract_la-table_s.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_la-table_s.lo `test -f 'tables/table_s.c' || echo '$(srcdir)/'`tables/table_s.c

libta_abstract_la-table_t.lo: tables/table_t.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_la-table_t.lo -MD -MP -MF $(DEPDIR)/libta_abstract_la-table_t.Tpo -c -o libta_abstract_la-table_t.lo `test -f 'tables/table_t.c' || echo '$(srcdir)/'`tables/table_t.c
	mv -f $(DEPDIR)/libta_abstract_la-table_t.Tpo $(DEPDIR)/libta_abstract_la-table_t.Plo
#	source='tables/table_t.c' object='libta_abstract_la-table_t.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_la-table_t.lo `test -f 'tables/table_t.c' || echo '$(srcdir)/'`tables/table_t.c

libta_abstract_la-table_u.lo: tables/table_u.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_la-table_u.lo -MD -MP -MF $(DEPDIR)/libta_abstract_la-table_u.Tpo -c -o libta_abstract_la-table_u.lo `test -f 'tables/table_u.c' || echo '$(srcdir)/'`tables/table_u.c
	mv -f $(DEPDIR)/libta_abstract_la-table_u.Tpo $(DEPDIR)/libta_abstract_la-table_u.Plo
#	source='tables/table_u.c' object='libta_abstract_la-table_u.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_la-table_u.lo `test -f 'tables/table_u.c' || echo '$(srcdir)/'`tables/table_u.c

libta_abstract_la-table_v.lo: tables/table_v.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_la-table_v.lo -MD -MP -MF $(DEPDIR)/libta_abstract_la-table_v.Tpo -c -o libta_abstract_la-table_v.lo `test -f 'tables/table_v.c' || echo '$(srcdir)/'`tables/table_v.c
	mv -f $(DEPDIR)/libta_abstract_la-table_v.Tpo $(DEPDIR)/libta_abstract_la-table_v.Plo
#	source='tables/table_v.c' object='libta_abstract_la-table_v.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_la-table_v.lo `test -f 'tables/table_v.c' || echo '$(srcdir)/'`tables/table_v.c

libta_abstract_la-table_w.lo: tables/table_w.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_la-table_w.lo -MD -MP -MF $(DEPDIR)/libta_abstract_la-table_w.Tpo -c -o libta_abstract_la-table_w.lo `test -f 'tables/table_w.c' || echo '$(srcdir)/'`tables/table_w.c
	mv -f $(DEPDIR)/libta_abstract_la-table_w.Tpo $(DEPDIR)/libta_abstract_la-table_w.Plo
#	source='tables/table_w.c' object='libta_abstract_la-table_w.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_la-table_w.lo `test -f 'tables/table_w.c' || echo '$(srcdir)/'`tables/table_w.c

libta_abstract_la-table_x.lo: tables/table_x.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_la-table_x.lo -MD -MP -MF $(DEPDIR)/libta_abstract_la-table_x.Tpo -c -o libta_abstract_la-table_x.lo `test -f 'tables/table_x.c' || echo '$(srcdir)/'`tables/table_x.c
	mv -f $(DEPDIR)/libta_abstract_la-table_x.Tpo $(DEPDIR)/libta_abstract_la-table_x.Plo
#	source='tables/table_x.c' object='libta_abstract_la-table_x.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_la-table_x.lo `test -f 'tables/table_x.c' || echo '$(srcdir)/'`tables/table_x.c

libta_abstract_la-table_y.lo: tables/table_y.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_la-table_y.lo -MD -MP -MF $(DEPDIR)/libta_abstract_la-table_y.Tpo -c -o libta_abstract_la-table_y.lo `test -f 'tables/table_y.c' || echo '$(srcdir)/'`tables/table_y.c
	mv -f $(DEPDIR)/libta_abstract_la-table_y.Tpo $(DEPDIR)/libta_abstract_la-table_y.Plo
#	source='tables/table_y.c' object='libta_abstract_la-table_y.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_la-table_y.lo `test -f 'tables/table_y.c' || echo '$(srcdir)/'`tables/table_y.c

libta_abstract_la-table_z.lo: tables/table_z.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_la-table_z.lo -MD -MP -MF $(DEPDIR)/libta_abstract_la-table_z.Tpo -c -o libta_abstract_la-table_z.lo `test -f 'tables/table_z.c' || echo '$(srcdir)/'`tables/table_z.c
	mv -f $(DEPDIR)/libta_abstract_la-table_z.Tpo $(DEPDIR)/libta_abstract_la-table_z.Plo
#	source='tables/table_z.c' object='libta_abstract_la-table_z.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_la-table_z.lo `test -f 'tables/table_z.c' || echo '$(srcdir)/'`tables/table_z.c

libta_abstract_gc_la-ta_group_idx.lo: ta_group_idx.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_gc_la-ta_group_idx.lo -MD -MP -MF $(DEPDIR)/libta_abstract_gc_la-ta_group_idx.Tpo -c -o libta_abstract_gc_la-ta_group_idx.lo `test -f 'ta_group_idx.c' || echo '$(srcdir)/'`ta_group_idx.c
	mv -f $(DEPDIR)/libta_abstract_gc_la-ta_group_idx.Tpo $(DEPDIR)/libta_abstract_gc_la-ta_group_idx.Plo
#	source='ta_group_idx.c' object='libta_abstract_gc_la-ta_group_idx.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_gc_la-ta_group_idx.lo `test -f 'ta_group_idx.c' || echo '$(srcdir)/'`ta_group_idx.c

libta_abstract_gc_la-ta_def_ui.lo: ta_def_ui.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_gc_la-ta_def_ui.lo -MD -MP -MF $(DEPDIR)/libta_abstract_gc_la-ta_def_ui.Tpo -c -o libta_abstract_gc_la-ta_def_ui.lo `test -f 'ta_def_ui.c' || echo '$(srcdir)/'`ta_def_ui.c
	mv -f $(DEPDIR)/libta_abstract_gc_la-ta_def_ui.Tpo $(DEPDIR)/libta_abstract_gc_la-ta_def_ui.Plo
#	source='ta_def_ui.c' object='libta_abstract_gc_la-ta_def_ui.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_gc_la-ta_def_ui.lo `test -f 'ta_def_ui.c' || echo '$(srcdir)/'`ta_def_ui.c

libta_abstract_gc_la-ta_abstract.lo: ta_abstract.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_gc_la-ta_abstract.lo -MD -MP -MF $(DEPDIR)/libta_abstract_gc_la-ta_abstract.Tpo -c -o libta_abstract_gc_la-ta_abstract.lo `test -f 'ta_abstract.c' || echo '$(srcdir)/'`ta_abstract.c
	mv -f $(DEPDIR)/libta_abstract_gc_la-ta_abstract.Tpo $(DEPDIR)/libta_abstract_gc_la-ta_abstract.Plo
#	source='ta_abstract.c' object='libta_abstract_gc_la-ta_abstract.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_gc_la-ta_abstract.lo `test -f 'ta_abstract.c' || echo '$(srcdir)/'`ta_abstract.c

libta_abstract_gc_la-ta_func_api.lo: ta_func_api.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_gc_la-ta_func_api.lo -MD -MP -MF $(DEPDIR)/libta_abstract_gc_la-ta_func_api.Tpo -c -o libta_abstract_gc_la-ta_func_api.lo `test -f 'ta_func_api.c' || echo '$(srcdir)/'`ta_func_api.c
	mv -f $(DEPDIR)/libta_abstract_gc_la-ta_func_api.Tpo $(DEPDIR)/libta_abstract_gc_la-ta_func_api.Plo
#	source='ta_func_api.c' object='libta_abstract_gc_la-ta_func_api.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_gc_la-ta_func_api.lo `test -f 'ta_func_api.c' || echo '$(srcdir)/'`ta_func_api.c

libta_abstract_gc_la-ta_frame.lo: frames/ta_frame.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_gc_la-ta_frame.lo -MD -MP -MF $(DEPDIR)/libta_abstract_gc_la-ta_frame.Tpo -c -o libta_abstract_gc_la-ta_frame.lo `test -f 'frames/ta_frame.c' || echo '$(srcdir)/'`frames/ta_frame.c
	mv -f $(DEPDIR)/libta_abstract_gc_la-ta_frame.Tpo $(DEPDIR)/libta_abstract_gc_la-ta_frame.Plo
#	source='frames/ta_frame.c' object='libta_abstract_gc_la-ta_frame.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_gc_la-ta_frame.lo `test -f 'frames/ta_frame.c' || echo '$(srcdir)/'`frames/ta_frame.c

libta_abstract_gc_la-table_a.lo: tables/table_a.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_gc_la-table_a.lo -MD -MP -MF $(DEPDIR)/libta_abstract_gc_la-table_a.Tpo -c -o libta_abstract_gc_la-table_a.lo `test -f 'tables/table_a.c' || echo '$(srcdir)/'`tables/table_a.c
	mv -f $(DEPDIR)/libta_abstract_gc_la-table_a.Tpo $(DEPDIR)/libta_abstract_gc_la-table_a.Plo
#	source='tables/table_a.c' object='libta_abstract_gc_la-table_a.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_gc_la-table_a.lo `test -f 'tables/table_a.c' || echo '$(srcdir)/'`tables/table_a.c

libta_abstract_gc_la-table_b.lo: tables/table_b.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_gc_la-table_b.lo -MD -MP -MF $(DEPDIR)/libta_abstract_gc_la-table_b.Tpo -c -o libta_abstract_gc_la-table_b.lo `test -f 'tables/table_b.c' || echo '$(srcdir)/'`tables/table_b.c
	mv -f $(DEPDIR)/libta_abstract_gc_la-table_b.Tpo $(DEPDIR)/libta_abstract_gc_la-table_b.Plo
#	source='tables/table_b.c' object='libta_abstract_gc_la-table_b.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_gc_la-table_b.lo `test -f 'tables/table_b.c' || echo '$(srcdir)/'`tables/table_b.c

libta_abstract_gc_la-table_c.lo: tables/table_c.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_gc_la-table_c.lo -MD -MP -MF $(DEPDIR)/libta_abstract_gc_la-table_c.Tpo -c -o libta_abstract_gc_la-table_c.lo `test -f 'tables/table_c.c' || echo '$(srcdir)/'`tables/table_c.c
	mv -f $(DEPDIR)/libta_abstract_gc_la-table_c.Tpo $(DEPDIR)/libta_abstract_gc_la-table_c.Plo
#	source='tables/table_c.c' object='libta_abstract_gc_la-table_c.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_gc_la-table_c.lo `test -f 'tables/table_c.c' || echo '$(srcdir)/'`tables/table_c.c

libta_abstract_gc_la-table_d.lo: tables/table_d.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_gc_la-table_d.lo -MD -MP -MF $(DEPDIR)/libta_abstract_gc_la-table_d.Tpo -c -o libta_abstract_gc_la-table_d.lo `test -f 'tables/table_d.c' || echo '$(srcdir)/'`tables/table_d.c
	mv -f $(DEPDIR)/libta_abstract_gc_la-table_d.Tpo $(DEPDIR)/libta_abstract_gc_la-table_d.Plo
#	source='tables/table_d.c' object='libta_abstract_gc_la-table_d.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_gc_la-table_d.lo `test -f 'tables/table_d.c' || echo '$(srcdir)/'`tables/table_d.c

libta_abstract_gc_la-table_e.lo: tables/table_e.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_gc_la-table_e.lo -MD -MP -MF $(DEPDIR)/libta_abstract_gc_la-table_e.Tpo -c -o libta_abstract_gc_la-table_e.lo `test -f 'tables/table_e.c' || echo '$(srcdir)/'`tables/table_e.c
	mv -f $(DEPDIR)/libta_abstract_gc_la-table_e.Tpo $(DEPDIR)/libta_abstract_gc_la-table_e.Plo
#	source='tables/table_e.c' object='libta_abstract_gc_la-table_e.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_gc_la-table_e.lo `test -f 'tables/table_e.c' || echo '$(srcdir)/'`tables/table_e.c

libta_abstract_gc_la-table_f.lo: tables/table_f.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_gc_la-table_f.lo -MD -MP -MF $(DEPDIR)/libta_abstract_gc_la-table_f.Tpo -c -o libta_abstract_gc_la-table_f.lo `test -f 'tables/table_f.c' || echo '$(srcdir)/'`tables/table_f.c
	mv -f $(DEPDIR)/libta_abstract_gc_la-table_f.Tpo $(DEPDIR)/libta_abstract_gc_la-table_f.Plo
#	source='tables/table_f.c' object='libta_abstract_gc_la-table_f.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_gc_la-table_f.lo `test -f 'tables/table_f.c' || echo '$(srcdir)/'`tables/table_f.c

libta_abstract_gc_la-table_g.lo: tables/table_g.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_gc_la-table_g.lo -MD -MP -MF $(DEPDIR)/libta_abstract_gc_la-table_g.Tpo -c -o libta_abstract_gc_la-table_g.lo `test -f 'tables/table_g.c' || echo '$(srcdir)/'`tables/table_g.c
	mv -f $(DEPDIR)/libta_abstract_gc_la-table_g.Tpo $(DEPDIR)/libta_abstract_gc_la-table_g.Plo
#	source='tables/table_g.c' object='libta_abstract_gc_la-table_g.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_gc_la-table_g.lo `test -f 'tables/table_g.c' || echo '$(srcdir)/'`tables/table_g.c

libta_abstract_gc_la-table_h.lo: tables/table_h.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_gc_la-table_h.lo -MD -MP -MF $(DEPDIR)/libta_abstract_gc_la-table_h.Tpo -c -o libta_abstract_gc_la-table_h.lo `test -f 'tables/table_h.c' || echo '$(srcdir)/'`tables/table_h.c
	mv -f $(DEPDIR)/libta_abstract_gc_la-table_h.Tpo $(DEPDIR)/libta_abstract_gc_la-table_h.Plo
#	source='tables/table_h.c' object='libta_abstract_gc_la-table_h.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_gc_la-table_h.lo `test -f 'tables/table_h.c' || echo '$(srcdir)/'`tables/table_h.c

libta_abstract_gc_la-table_i.lo: tables/table_i.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_gc_la-table_i.lo -MD -MP -MF $(DEPDIR)/libta_abstract_gc_la-table_i.Tpo -c -o libta_abstract_gc_la-table_i.lo `test -f 'tables/table_i.c' || echo '$(srcdir)/'`tables/table_i.c
	mv -f $(DEPDIR)/libta_abstract_gc_la-table_i.Tpo $(DEPDIR)/libta_abstract_gc_la-table_i.Plo
#	source='tables/table_i.c' object='libta_abstract_gc_la-table_i.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_gc_la-table_i.lo `test -f 'tables/table_i.c' || echo '$(srcdir)/'`tables/table_i.c

libta_abstract_gc_la-table_j.lo: tables/table_j.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_gc_la-table_j.lo -MD -MP -MF $(DEPDIR)/libta_abstract_gc_la-table_j.Tpo -c -o libta_abstract_gc_la-table_j.lo `test -f 'tables/table_j.c' || echo '$(srcdir)/'`tables/table_j.c
	mv -f $(DEPDIR)/libta_abstract_gc_la-table_j.Tpo $(DEPDIR)/libta_abstract_gc_la-table_j.Plo
#	source='tables/table_j.c' object='libta_abstract_gc_la-table_j.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_gc_la-table_j.lo `test -f 'tables/table_j.c' || echo '$(srcdir)/'`tables/table_j.c

libta_abstract_gc_la-table_k.lo: tables/table_k.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_gc_la-table_k.lo -MD -MP -MF $(DEPDIR)/libta_abstract_gc_la-table_k.Tpo -c -o libta_abstract_gc_la-table_k.lo `test -f 'tables/table_k.c' || echo '$(srcdir)/'`tables/table_k.c
	mv -f $(DEPDIR)/libta_abstract_gc_la-table_k.Tpo $(DEPDIR)/libta_abstract_gc_la-table_k.Plo
#	source='tables/table_k.c' object='libta_abstract_gc_la-table_k.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_gc_la-table_k.lo `test -f 'tables/table_k.c' || echo '$(srcdir)/'`tables/table_k.c

libta_abstract_gc_la-table_l.lo: tables/table_l.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_gc_la-table_l.lo -MD -MP -MF $(DEPDIR)/libta_abstract_gc_la-table_l.Tpo -c -o libta_abstract_gc_la-table_l.lo `test -f 'tables/table_l.c' || echo '$(srcdir)/'`tables/table_l.c
	mv -f $(DEPDIR)/libta_abstract_gc_la-table_l.Tpo $(DEPDIR)/libta_abstract_gc_la-table_l.Plo
#	source='tables/table_l.c' object='libta_abstract_gc_la-table_l.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_gc_la-table_l.lo `test -f 'tables/table_l.c' || echo '$(srcdir)/'`tables/table_l.c

libta_abstract_gc_la-table_m.lo: tables/table_m.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_gc_la-table_m.lo -MD -MP -MF $(DEPDIR)/libta_abstract_gc_la-table_m.Tpo -c -o libta_abstract_gc_la-table_m.lo `test -f 'tables/table_m.c' || echo '$(srcdir)/'`tables/table_m.c
	mv -f $(DEPDIR)/libta_abstract_gc_la-table_m.Tpo $(DEPDIR)/libta_abstract_gc_la-table_m.Plo
#	source='tables/table_m.c' object='libta_abstract_gc_la-table_m.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_gc_la-table_m.lo `test -f 'tables/table_m.c' || echo '$(srcdir)/'`tables/table_m.c

libta_abstract_gc_la-table_n.lo: tables/table_n.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_gc_la-table_n.lo -MD -MP -MF $(DEPDIR)/libta_abstract_gc_la-table_n.Tpo -c -o libta_abstract_gc_la-table_n.lo `test -f 'tables/table_n.c' || echo '$(srcdir)/'`tables/table_n.c
	mv -f $(DEPDIR)/libta_abstract_gc_la-table_n.Tpo $(DEPDIR)/libta_abstract_gc_la-table_n.Plo
#	source='tables/table_n.c' object='libta_abstract_gc_la-table_n.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_gc_la-table_n.lo `test -f 'tables/table_n.c' || echo '$(srcdir)/'`tables/table_n.c

libta_abstract_gc_la-table_o.lo: tables/table_o.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_gc_la-table_o.lo -MD -MP -MF $(DEPDIR)/libta_abstract_gc_la-table_o.Tpo -c -o libta_abstract_gc_la-table_o.lo `test -f 'tables/table_o.c' || echo '$(srcdir)/'`tables/table_o.c
	mv -f $(DEPDIR)/libta_abstract_gc_la-table_o.Tpo $(DEPDIR)/libta_abstract_gc_la-table_o.Plo
#	source='tables/table_o.c' object='libta_abstract_gc_la-table_o.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_gc_la-table_o.lo `test -f 'tables/table_o.c' || echo '$(srcdir)/'`tables/table_o.c

libta_abstract_gc_la-table_p.lo: tables/table_p.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_gc_la-table_p.lo -MD -MP -MF $(DEPDIR)/libta_abstract_gc_la-table_p.Tpo -c -o libta_abstract_gc_la-table_p.lo `test -f 'tables/table_p.c' || echo '$(srcdir)/'`tables/table_p.c
	mv -f $(DEPDIR)/libta_abstract_gc_la-table_p.Tpo $(DEPDIR)/libta_abstract_gc_la-table_p.Plo
#	source='tables/table_p.c' object='libta_abstract_gc_la-table_p.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_gc_la-table_p.lo `test -f 'tables/table_p.c' || echo '$(srcdir)/'`tables/table_p.c

libta_abstract_gc_la-table_q.lo: tables/table_q.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_gc_la-table_q.lo -MD -MP -MF $(DEPDIR)/libta_abstract_gc_la-table_q.Tpo -c -o libta_abstract_gc_la-table_q.lo `test -f 'tables/table_q.c' || echo '$(srcdir)/'`tables/table_q.c
	mv -f $(DEPDIR)/libta_abstract_gc_la-table_q.Tpo $(DEPDIR)/libta_abstract_gc_la-table_q.Plo
#	source='tables/table_q.c' object='libta_abstract_gc_la-table_q.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_gc_la-table_q.lo `test -f 'tables/table_q.c' || echo '$(srcdir)/'`tables/table_q.c

libta_abstract_gc_la-table_r.lo: tables/table_r.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_gc_la-table_r.lo -MD -MP -MF $(DEPDIR)/libta_abstract_gc_la-table_r.Tpo -c -o libta_abstract_gc_la-table_r.lo `test -f 'tables/table_r.c' || echo '$(srcdir)/'`tables/table_r.c
	mv -f $(DEPDIR)/libta_abstract_gc_la-table_r.Tpo $(DEPDIR)/libta_abstract_gc_la-table_r.Plo
#	source='tables/table_r.c' object='libta_abstract_gc_la-table_r.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_gc_la-table_r.lo `test -f 'tables/table_r.c' || echo '$(srcdir)/'`tables/table_r.c

libta_abstract_gc_la-table_s.lo: tables/table_s.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_gc_la-table_s.lo -MD -MP -MF $(DEPDIR)/libta_abstract_gc_la-table_s.Tpo -c -o libta_abstract_gc_la-table_s.lo `test -f 'tables/table_s.c' || echo '$(srcdir)/'`tables/table_s.c
	mv -f $(DEPDIR)/libta_abstract_gc_la-table_s.Tpo $(DEPDIR)/libta_abstract_gc_la-table_s.Plo
#	source='tables/table_s.c' object='libta_abstract_gc_la-table_s.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_gc_la-table_s.lo `test -f 'tables/table_s.c' || echo '$(srcdir)/'`tables/table_s.c

libta_abstract_gc_la-table_t.lo: tables/table_t.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_gc_la-table_t.lo -MD -MP -MF $(DEPDIR)/libta_abstract_gc_la-table_t.Tpo -c -o libta_abstract_gc_la-table_t.lo `test -f 'tables/table_t.c' || echo '$(srcdir)/'`tables/table_t.c
	mv -f $(DEPDIR)/libta_abstract_gc_la-table_t.Tpo $(DEPDIR)/libta_abstract_gc_la-table_t.Plo
#	source='tables/table_t.c' object='libta_abstract_gc_la-table_t.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_gc_la-table_t.lo `test -f 'tables/table_t.c' || echo '$(srcdir)/'`tables/table_t.c

libta_abstract_gc_la-table_u.lo: tables/table_u.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_gc_la-table_u.lo -MD -MP -MF $(DEPDIR)/libta_abstract_gc_la-table_u.Tpo -c -o libta_abstract_gc_la-table_u.lo `test -f 'tables/table_u.c' || echo '$(srcdir)/'`tables/table_u.c
	mv -f $(DEPDIR)/libta_abstract_gc_la-table_u.Tpo $(DEPDIR)/libta_abstract_gc_la-table_u.Plo
#	source='tables/table_u.c' object='libta_abstract_gc_la-table_u.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_gc_la-table_u.lo `test -f 'tables/table_u.c' || echo '$(srcdir)/'`tables/table_u.c

libta_abstract_gc_la-table_v.lo: tables/table_v.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_gc_la-table_v.lo -MD -MP -MF $(DEPDIR)/libta_abstract_gc_la-table_v.Tpo -c -o libta_abstract_gc_la-table_v.lo `test -f 'tables/table_v.c' || echo '$(srcdir)/'`tables/table_v.c
	mv -f $(DEPDIR)/libta_abstract_gc_la-table_v.Tpo $(DEPDIR)/libta_abstract_gc_la-table_v.Plo
#	source='tables/table_v.c' object='libta_abstract_gc_la-table_v.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_gc_la-table_v.lo `test -f 'tables/table_v.c' || echo '$(srcdir)/'`tables/table_v.c

libta_abstract_gc_la-table_w.lo: tables/table_w.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_gc_la-table_w.lo -MD -MP -MF $(DEPDIR)/libta_abstract_gc_la-table_w.Tpo -c -o libta_abstract_gc_la-table_w.lo `test -f 'tables/table_w.c' || echo '$(srcdir)/'`tables/table_w.c
	mv -f $(DEPDIR)/libta_abstract_gc_la-table_w.Tpo $(DEPDIR)/libta_abstract_gc_la-table_w.Plo
#	source='tables/table_w.c' object='libta_abstract_gc_la-table_w.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_gc_la-table_w.lo `test -f 'tables/table_w.c' || echo '$(srcdir)/'`tables/table_w.c

libta_abstract_gc_la-table_x.lo: tables/table_x.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_gc_la-table_x.lo -MD -MP -MF $(DEPDIR)/libta_abstract_gc_la-table_x.Tpo -c -o libta_abstract_gc_la-table_x.lo `test -f 'tables/table_x.c' || echo '$(srcdir)/'`tables/table_x.c
	mv -f $(DEPDIR)/libta_abstract_gc_la-table_x.Tpo $(DEPDIR)/libta_abstract_gc_la-table_x.Plo
#	source='tables/table_x.c' object='libta_abstract_gc_la-table_x.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_gc_la-table_x.lo `test -f 'tables/table_x.c' || echo '$(srcdir)/'`tables/table_x.c

libta_abstract_gc_la-table_y.lo: tables/table_y.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_gc_la-table_y.lo -MD -MP -MF $(DEPDIR)/libta_abstract_gc_la-table_y.Tpo -c -o libta_abstract_gc_la-table_y.lo `test -f 'tables/table_y.c' || echo '$(srcdir)/'`tables/table_y.c
	mv -f $(DEPDIR)/libta_abstract_gc_la-table_y.Tpo $(DEPDIR)/libta_abstract_gc_la-table_y.Plo
#	source='tables/table_y.c' object='libta_abstract_gc_la-table_y.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_gc_la-table_y.lo `test -f 'tables/table_y.c' || echo '$(srcdir)/'`tables/table_y.c

libta_abstract_gc_la-table_z.lo: tables/table_z.c
	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT libta_abstract_gc_la-table_z.lo -MD -MP -MF $(DEPDIR)/libta_abstract_gc_la-table_z.Tpo -c -o libta_abstract_gc_la-table_z.lo `test -f 'tables/table_z.c' || echo '$(srcdir)/'`tables/table_z.c
	mv -f $(DEPDIR)/libta_abstract_gc_la-table_z.Tpo $(DEPDIR)/libta_abstract_gc_la-table_z.Plo
#	source='tables/table_z.c' object='libta_abstract_gc_la-table_z.lo' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(libta_abstract_gc_la_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o libta_abstract_gc_la-table_z.lo `test -f 'tables/table_z.c' || echo '$(srcdir)/'`tables/table_z.c

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs
install-libta_abstractHEADERS: $(libta_abstract_HEADERS)
	@$(NORMAL_INSTALL)
	test -z "$(libta_abstractdir)" || $(MKDIR_P) "$(DESTDIR)$(libta_abstractdir)"
	@list='$(libta_abstract_HEADERS)'; for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  f=$(am__strip_dir) \
	  echo " $(libta_abstractHEADERS_INSTALL) '$$d$$p' '$(DESTDIR)$(libta_abstractdir)/$$f'"; \
	  $(libta_abstractHEADERS_INSTALL) "$$d$$p" "$(DESTDIR)$(libta_abstractdir)/$$f"; \
	done

uninstall-libta_abstractHEADERS:
	@$(NORMAL_UNINSTALL)
	@list='$(libta_abstract_HEADERS)'; for p in $$list; do \
	  f=$(am__strip_dir) \
	  echo " rm -f '$(DESTDIR)$(libta_abstractdir)/$$f'"; \
	  rm -f "$(DESTDIR)$(libta_abstractdir)/$$f"; \
	done

ID: $(HEADERS) $(SOURCES) $(LISP) $(TAGS_FILES)
	list='$(SOURCES) $(HEADERS) $(LISP) $(TAGS_FILES)'; \
	unique=`for i in $$list; do \
	    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
	  done | \
	  $(AWK) '    { files[$$0] = 1; } \
	       END { for (i in files) print i; }'`; \
	mkid -fID $$unique
tags: TAGS

TAGS:  $(HEADERS) $(SOURCES)  $(TAGS_DEPENDENCIES) \
		$(TAGS_FILES) $(LISP)
	tags=; \
	here=`pwd`; \
	list='$(SOURCES) $(HEADERS)  $(LISP) $(TAGS_FILES)'; \
	unique=`for i in $$list; do \
	    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
	  done | \
	  $(AWK) '    { files[$$0] = 1; } \
	       END { for (i in files) print i; }'`; \
	if test -z "$(ETAGS_ARGS)$$tags$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	    $$tags $$unique; \
	fi
ctags: CTAGS
CTAGS:  $(HEADERS) $(SOURCES)  $(TAGS_DEPENDENCIES) \
		$(TAGS_FILES) $(LISP)
	tags=; \
	here=`pwd`; \
	list='$(SOURCES) $(HEADERS)  $(LISP) $(TAGS_FILES)'; \
	unique=`for i in $$list; do \
	    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
	  done | \
	  $(AWK) '    { files[$$0] = 1; } \
	       END { for (i in files) print i; }'`; \
	test -z "$(CTAGS_ARGS)$$tags$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$tags $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && cd $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) $$here

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags

distdir: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -pR $(srcdir)/$$file $(distdir)$$dir || exit 1; \
	    fi; \
	    cp -pR $$d/$$file $(distdir)$$dir || exit 1; \
	  else \
	    test -f $(distdir)/$$file \
	    || cp -p $$d/$$file $(distdir)/$$file \
	    || exit 1; \
	  fi; \
	done
check-am: all-am
check: check-am
all-am: Makefile $(LTLIBRARIES) $(HEADERS)
installdirs:
	for dir in "$(DESTDIR)$(libta_abstractdir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	$(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	  install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	  `test -z '$(STRIP)' || \
	    echo "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'"` install
mostlyclean-generic:

clean-generic:

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
clean: clean-am

clean-am: clean-generic clean-libtool clean-noinstLTLIBRARIES \
	mostlyclean-am

distclean: distclean-am
	-rm -rf ./$(DEPDIR)
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-tags

dvi: dvi-am

dvi-am:

html: html-am

info: info-am

info-am:

install-data-am: install-libta_abstractHEADERS

install-dvi: install-dvi-am

install-exec-am:

install-html: install-html-am

install-info: install-info-am

install-man:

install-pdf: install-pdf-am

install-ps: install-ps-am

installcheck-am:

maintainer-clean: maintainer-clean-am
	-rm -rf ./$(DEPDIR)
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am: uninstall-libta_abstractHEADERS

.MAKE: install-am install-strip

.PHONY: CTAGS GTAGS all all-am check check-am clean clean-generic \
	clean-libtool clean-noinstLTLIBRARIES ctags distclean \
	distclean-compile distclean-generic distclean-libtool \
	distclean-tags distdir dvi dvi-am html html-am info info-am \
	install install-am install-data install-data-am install-dvi \
	install-dvi-am install-exec install-exec-am install-html \
	install-html-am install-info install-info-am \
	install-libta_abstractHEADERS install-man install-pdf \
	install-pdf-am install-ps install-ps-am install-strip \
	installcheck installcheck-am installdirs maintainer-clean \
	maintainer-clean-generic mostlyclean mostlyclean-compile \
	mostlyclean-generic mostlyclean-libtool pdf pdf-am ps ps-am \
	tags uninstall uninstall-am uninstall-libta_abstractHEADERS

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:
