# Makefile.in generated by automake 1.10 from Makefile.am.
# @configure_input@

# Copyright (C) 1994, 1995, 1996, 1997, 1998, 1999, 2000, 2001, 2002,
# 2003, 2004, 2005, 2006  Free Software Foundation, Inc.
# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.

@SET_MAKE@

VPATH = @srcdir@
pkgdatadir = $(datadir)/@PACKAGE@
pkglibdir = $(libdir)/@PACKAGE@
pkgincludedir = $(includedir)/@PACKAGE@
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = @build@
host_triplet = @host@
noinst_PROGRAMS = ta_regtest$(EXEEXT)
subdir = src/tools/ta_regtest
DIST_COMMON = $(srcdir)/Makefile.am $(srcdir)/Makefile.in
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/configure.in
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/include/ta_config.h
CONFIG_CLEAN_FILES =
PROGRAMS = $(noinst_PROGRAMS)
am_ta_regtest_OBJECTS = ta_regtest-ta_regtest.$(OBJEXT) \
	ta_regtest-test_data.$(OBJEXT) ta_regtest-test_util.$(OBJEXT) \
	ta_regtest-test_abstract.$(OBJEXT) \
	ta_regtest-test_adx.$(OBJEXT) ta_regtest-test_mom.$(OBJEXT) \
	ta_regtest-test_sar.$(OBJEXT) ta_regtest-test_rsi.$(OBJEXT) \
	ta_regtest-test_candlestick.$(OBJEXT) \
	ta_regtest-test_per_ema.$(OBJEXT) \
	ta_regtest-test_per_hlc.$(OBJEXT) \
	ta_regtest-test_stoch.$(OBJEXT) ta_regtest-test_macd.$(OBJEXT) \
	ta_regtest-test_minmax.$(OBJEXT) \
	ta_regtest-test_per_hlcv.$(OBJEXT) \
	ta_regtest-test_1in_1out.$(OBJEXT) \
	ta_regtest-test_1in_2out.$(OBJEXT) \
	ta_regtest-test_per_ohlc.$(OBJEXT) \
	ta_regtest-test_stddev.$(OBJEXT) \
	ta_regtest-test_bbands.$(OBJEXT) ta_regtest-test_ma.$(OBJEXT) \
	ta_regtest-test_po.$(OBJEXT) ta_regtest-test_per_hl.$(OBJEXT) \
	ta_regtest-test_trange.$(OBJEXT) \
	ta_regtest-test_internals.$(OBJEXT)
ta_regtest_OBJECTS = $(am_ta_regtest_OBJECTS)
ta_regtest_LDADD = $(LDADD)
ta_regtest_LINK = $(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(ta_regtest_LDFLAGS) $(LDFLAGS) -o $@
DEFAULT_INCLUDES = -I. -I$(top_builddir)/include@am__isrc@
depcomp = $(SHELL) $(top_srcdir)/depcomp
am__depfiles_maybe = depfiles
COMPILE = $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) \
	$(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
LTCOMPILE = $(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) \
	--mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) \
	$(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
CCLD = $(CC)
LINK = $(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) \
	--mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) $(AM_LDFLAGS) \
	$(LDFLAGS) -o $@
SOURCES = $(ta_regtest_SOURCES)
DIST_SOURCES = $(ta_regtest_SOURCES)
ETAGS = etags
CTAGS = ctags
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
ACLOCAL = @ACLOCAL@
AMTAR = @AMTAR@
AR = @AR@
AUTOCONF = @AUTOCONF@
AUTOHEADER = @AUTOHEADER@
AUTOMAKE = @AUTOMAKE@
AWK = @AWK@
CC = @CC@
CCDEPMODE = @CCDEPMODE@
CFLAGS = @CFLAGS@
CPP = @CPP@
CPPFLAGS = @CPPFLAGS@
CXX = @CXX@
CXXCPP = @CXXCPP@
CXXDEPMODE = @CXXDEPMODE@
CXXFLAGS = @CXXFLAGS@
CYGPATH_W = @CYGPATH_W@
DEFS = @DEFS@
DEPDIR = @DEPDIR@
ECHO = @ECHO@
ECHO_C = @ECHO_C@
ECHO_N = @ECHO_N@
ECHO_T = @ECHO_T@
EGREP = @EGREP@
EXEEXT = @EXEEXT@
F77 = @F77@
FFLAGS = @FFLAGS@
GREP = @GREP@
INSTALL = @INSTALL@
INSTALL_DATA = @INSTALL_DATA@
INSTALL_PROGRAM = @INSTALL_PROGRAM@
INSTALL_SCRIPT = @INSTALL_SCRIPT@
INSTALL_STRIP_PROGRAM = @INSTALL_STRIP_PROGRAM@
LDFLAGS = @LDFLAGS@
LIBOBJS = @LIBOBJS@
LIBS = @LIBS@
LIBTOOL = @LIBTOOL@
LN_S = @LN_S@
LTLIBOBJS = @LTLIBOBJS@
MAKEINFO = @MAKEINFO@
MKDIR_P = @MKDIR_P@
OBJEXT = @OBJEXT@
PACKAGE = @PACKAGE@
PACKAGE_BUGREPORT = @PACKAGE_BUGREPORT@
PACKAGE_NAME = @PACKAGE_NAME@
PACKAGE_STRING = @PACKAGE_STRING@
PACKAGE_TARNAME = @PACKAGE_TARNAME@
PACKAGE_VERSION = @PACKAGE_VERSION@
PATH_SEPARATOR = @PATH_SEPARATOR@
POW_LIB = @POW_LIB@
RANLIB = @RANLIB@
SET_MAKE = @SET_MAKE@
SHELL = @SHELL@
STRIP = @STRIP@
TALIB_LIBRARY_VERSION = @TALIB_LIBRARY_VERSION@
VERSION = @VERSION@
abs_builddir = @abs_builddir@
abs_srcdir = @abs_srcdir@
abs_top_builddir = @abs_top_builddir@
abs_top_srcdir = @abs_top_srcdir@
ac_ct_CC = @ac_ct_CC@
ac_ct_CXX = @ac_ct_CXX@
ac_ct_F77 = @ac_ct_F77@
am__include = @am__include@
am__leading_dot = @am__leading_dot@
am__quote = @am__quote@
am__tar = @am__tar@
am__untar = @am__untar@
bindir = @bindir@
build = @build@
build_alias = @build_alias@
build_cpu = @build_cpu@
build_os = @build_os@
build_vendor = @build_vendor@
builddir = @builddir@
datadir = @datadir@
datarootdir = @datarootdir@
docdir = @docdir@
dvidir = @dvidir@
exec_prefix = @exec_prefix@
host = @host@
host_alias = @host_alias@
host_cpu = @host_cpu@
host_os = @host_os@
host_vendor = @host_vendor@
htmldir = @htmldir@
includedir = @includedir@
infodir = @infodir@
install_sh = @install_sh@
libdir = @libdir@
libexecdir = @libexecdir@
localedir = @localedir@
localstatedir = @localstatedir@
mandir = @mandir@
mkdir_p = @mkdir_p@
oldincludedir = @oldincludedir@
pdfdir = @pdfdir@
prefix = @prefix@
program_transform_name = @program_transform_name@
psdir = @psdir@
sbindir = @sbindir@
sharedstatedir = @sharedstatedir@
srcdir = @srcdir@
sysconfdir = @sysconfdir@
target_alias = @target_alias@
top_builddir = @top_builddir@
top_srcdir = @top_srcdir@
ta_regtest_SOURCES = ta_regtest.c \
	test_data.c \
	test_util.c \
	test_abstract.c \
	ta_test_func/test_adx.c \
	ta_test_func/test_mom.c \
	ta_test_func/test_sar.c \
	ta_test_func/test_rsi.c \
	ta_test_func/test_candlestick.c \
	ta_test_func/test_per_ema.c \
	ta_test_func/test_per_hlc.c \
	ta_test_func/test_stoch.c \
	ta_test_func/test_macd.c \
	ta_test_func/test_minmax.c \
	ta_test_func/test_per_hlcv.c \
	ta_test_func/test_1in_1out.c \
	ta_test_func/test_1in_2out.c \
	ta_test_func/test_per_ohlc.c \
	ta_test_func/test_stddev.c \
	ta_test_func/test_bbands.c \
	ta_test_func/test_ma.c \
	ta_test_func/test_po.c \
	ta_test_func/test_per_hl.c \
	ta_test_func/test_trange.c \
	test_internals.c

ta_regtest_CPPFLAGS = -I../../ta_func \
		      -I../../ta_common/trio \
		      -I../../ta_common/mt \
		      -I../../ta_common \
		      -I../../ta_abstract

ta_regtest_LDFLAGS = -L../.. -lta_lib \
		     -lm

all: all-am

.SUFFIXES:
.SUFFIXES: .c .lo .o .obj
$(srcdir)/Makefile.in:  $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh \
		&& exit 0; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --gnu  src/tools/ta_regtest/Makefile'; \
	cd $(top_srcdir) && \
	  $(AUTOMAKE) --gnu  src/tools/ta_regtest/Makefile
.PRECIOUS: Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__depfiles_maybe)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__depfiles_maybe);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure:  $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4):  $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

clean-noinstPROGRAMS:
	@list='$(noinst_PROGRAMS)'; for p in $$list; do \
	  f=`echo $$p|sed 's/$(EXEEXT)$$//'`; \
	  echo " rm -f $$p $$f"; \
	  rm -f $$p $$f ; \
	done
ta_regtest$(EXEEXT): $(ta_regtest_OBJECTS) $(ta_regtest_DEPENDENCIES) 
	@rm -f ta_regtest$(EXEEXT)
	$(ta_regtest_LINK) $(ta_regtest_OBJECTS) $(ta_regtest_LDADD) $(LIBS)

mostlyclean-compile:
	-rm -f *.$(OBJEXT)

distclean-compile:
	-rm -f *.tab.c

@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_regtest-ta_regtest.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_regtest-test_1in_1out.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_regtest-test_1in_2out.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_regtest-test_abstract.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_regtest-test_adx.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_regtest-test_bbands.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_regtest-test_candlestick.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_regtest-test_data.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_regtest-test_internals.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_regtest-test_ma.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_regtest-test_macd.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_regtest-test_minmax.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_regtest-test_mom.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_regtest-test_per_ema.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_regtest-test_per_hl.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_regtest-test_per_hlc.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_regtest-test_per_hlcv.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_regtest-test_per_ohlc.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_regtest-test_po.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_regtest-test_rsi.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_regtest-test_sar.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_regtest-test_stddev.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_regtest-test_stoch.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_regtest-test_trange.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/ta_regtest-test_util.Po@am__quote@

.c.o:
@am__fastdepCC_TRUE@	$(COMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ $<
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(COMPILE) -c $<

.c.obj:
@am__fastdepCC_TRUE@	$(COMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ `$(CYGPATH_W) '$<'`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(COMPILE) -c `$(CYGPATH_W) '$<'`

.c.lo:
@am__fastdepCC_TRUE@	$(LTCOMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ $<
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='$<' object='$@' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(LTCOMPILE) -c -o $@ $<

ta_regtest-ta_regtest.o: ta_regtest.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-ta_regtest.o -MD -MP -MF $(DEPDIR)/ta_regtest-ta_regtest.Tpo -c -o ta_regtest-ta_regtest.o `test -f 'ta_regtest.c' || echo '$(srcdir)/'`ta_regtest.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-ta_regtest.Tpo $(DEPDIR)/ta_regtest-ta_regtest.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ta_regtest.c' object='ta_regtest-ta_regtest.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-ta_regtest.o `test -f 'ta_regtest.c' || echo '$(srcdir)/'`ta_regtest.c

ta_regtest-ta_regtest.obj: ta_regtest.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-ta_regtest.obj -MD -MP -MF $(DEPDIR)/ta_regtest-ta_regtest.Tpo -c -o ta_regtest-ta_regtest.obj `if test -f 'ta_regtest.c'; then $(CYGPATH_W) 'ta_regtest.c'; else $(CYGPATH_W) '$(srcdir)/ta_regtest.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-ta_regtest.Tpo $(DEPDIR)/ta_regtest-ta_regtest.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ta_regtest.c' object='ta_regtest-ta_regtest.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-ta_regtest.obj `if test -f 'ta_regtest.c'; then $(CYGPATH_W) 'ta_regtest.c'; else $(CYGPATH_W) '$(srcdir)/ta_regtest.c'; fi`

ta_regtest-test_data.o: test_data.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_data.o -MD -MP -MF $(DEPDIR)/ta_regtest-test_data.Tpo -c -o ta_regtest-test_data.o `test -f 'test_data.c' || echo '$(srcdir)/'`test_data.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_data.Tpo $(DEPDIR)/ta_regtest-test_data.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='test_data.c' object='ta_regtest-test_data.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_data.o `test -f 'test_data.c' || echo '$(srcdir)/'`test_data.c

ta_regtest-test_data.obj: test_data.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_data.obj -MD -MP -MF $(DEPDIR)/ta_regtest-test_data.Tpo -c -o ta_regtest-test_data.obj `if test -f 'test_data.c'; then $(CYGPATH_W) 'test_data.c'; else $(CYGPATH_W) '$(srcdir)/test_data.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_data.Tpo $(DEPDIR)/ta_regtest-test_data.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='test_data.c' object='ta_regtest-test_data.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_data.obj `if test -f 'test_data.c'; then $(CYGPATH_W) 'test_data.c'; else $(CYGPATH_W) '$(srcdir)/test_data.c'; fi`

ta_regtest-test_util.o: test_util.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_util.o -MD -MP -MF $(DEPDIR)/ta_regtest-test_util.Tpo -c -o ta_regtest-test_util.o `test -f 'test_util.c' || echo '$(srcdir)/'`test_util.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_util.Tpo $(DEPDIR)/ta_regtest-test_util.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='test_util.c' object='ta_regtest-test_util.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_util.o `test -f 'test_util.c' || echo '$(srcdir)/'`test_util.c

ta_regtest-test_util.obj: test_util.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_util.obj -MD -MP -MF $(DEPDIR)/ta_regtest-test_util.Tpo -c -o ta_regtest-test_util.obj `if test -f 'test_util.c'; then $(CYGPATH_W) 'test_util.c'; else $(CYGPATH_W) '$(srcdir)/test_util.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_util.Tpo $(DEPDIR)/ta_regtest-test_util.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='test_util.c' object='ta_regtest-test_util.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_util.obj `if test -f 'test_util.c'; then $(CYGPATH_W) 'test_util.c'; else $(CYGPATH_W) '$(srcdir)/test_util.c'; fi`

ta_regtest-test_abstract.o: test_abstract.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_abstract.o -MD -MP -MF $(DEPDIR)/ta_regtest-test_abstract.Tpo -c -o ta_regtest-test_abstract.o `test -f 'test_abstract.c' || echo '$(srcdir)/'`test_abstract.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_abstract.Tpo $(DEPDIR)/ta_regtest-test_abstract.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='test_abstract.c' object='ta_regtest-test_abstract.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_abstract.o `test -f 'test_abstract.c' || echo '$(srcdir)/'`test_abstract.c

ta_regtest-test_abstract.obj: test_abstract.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_abstract.obj -MD -MP -MF $(DEPDIR)/ta_regtest-test_abstract.Tpo -c -o ta_regtest-test_abstract.obj `if test -f 'test_abstract.c'; then $(CYGPATH_W) 'test_abstract.c'; else $(CYGPATH_W) '$(srcdir)/test_abstract.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_abstract.Tpo $(DEPDIR)/ta_regtest-test_abstract.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='test_abstract.c' object='ta_regtest-test_abstract.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_abstract.obj `if test -f 'test_abstract.c'; then $(CYGPATH_W) 'test_abstract.c'; else $(CYGPATH_W) '$(srcdir)/test_abstract.c'; fi`

ta_regtest-test_adx.o: ta_test_func/test_adx.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_adx.o -MD -MP -MF $(DEPDIR)/ta_regtest-test_adx.Tpo -c -o ta_regtest-test_adx.o `test -f 'ta_test_func/test_adx.c' || echo '$(srcdir)/'`ta_test_func/test_adx.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_adx.Tpo $(DEPDIR)/ta_regtest-test_adx.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ta_test_func/test_adx.c' object='ta_regtest-test_adx.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_adx.o `test -f 'ta_test_func/test_adx.c' || echo '$(srcdir)/'`ta_test_func/test_adx.c

ta_regtest-test_adx.obj: ta_test_func/test_adx.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_adx.obj -MD -MP -MF $(DEPDIR)/ta_regtest-test_adx.Tpo -c -o ta_regtest-test_adx.obj `if test -f 'ta_test_func/test_adx.c'; then $(CYGPATH_W) 'ta_test_func/test_adx.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_adx.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_adx.Tpo $(DEPDIR)/ta_regtest-test_adx.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ta_test_func/test_adx.c' object='ta_regtest-test_adx.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_adx.obj `if test -f 'ta_test_func/test_adx.c'; then $(CYGPATH_W) 'ta_test_func/test_adx.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_adx.c'; fi`

ta_regtest-test_mom.o: ta_test_func/test_mom.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_mom.o -MD -MP -MF $(DEPDIR)/ta_regtest-test_mom.Tpo -c -o ta_regtest-test_mom.o `test -f 'ta_test_func/test_mom.c' || echo '$(srcdir)/'`ta_test_func/test_mom.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_mom.Tpo $(DEPDIR)/ta_regtest-test_mom.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ta_test_func/test_mom.c' object='ta_regtest-test_mom.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_mom.o `test -f 'ta_test_func/test_mom.c' || echo '$(srcdir)/'`ta_test_func/test_mom.c

ta_regtest-test_mom.obj: ta_test_func/test_mom.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_mom.obj -MD -MP -MF $(DEPDIR)/ta_regtest-test_mom.Tpo -c -o ta_regtest-test_mom.obj `if test -f 'ta_test_func/test_mom.c'; then $(CYGPATH_W) 'ta_test_func/test_mom.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_mom.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_mom.Tpo $(DEPDIR)/ta_regtest-test_mom.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ta_test_func/test_mom.c' object='ta_regtest-test_mom.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_mom.obj `if test -f 'ta_test_func/test_mom.c'; then $(CYGPATH_W) 'ta_test_func/test_mom.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_mom.c'; fi`

ta_regtest-test_sar.o: ta_test_func/test_sar.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_sar.o -MD -MP -MF $(DEPDIR)/ta_regtest-test_sar.Tpo -c -o ta_regtest-test_sar.o `test -f 'ta_test_func/test_sar.c' || echo '$(srcdir)/'`ta_test_func/test_sar.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_sar.Tpo $(DEPDIR)/ta_regtest-test_sar.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ta_test_func/test_sar.c' object='ta_regtest-test_sar.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_sar.o `test -f 'ta_test_func/test_sar.c' || echo '$(srcdir)/'`ta_test_func/test_sar.c

ta_regtest-test_sar.obj: ta_test_func/test_sar.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_sar.obj -MD -MP -MF $(DEPDIR)/ta_regtest-test_sar.Tpo -c -o ta_regtest-test_sar.obj `if test -f 'ta_test_func/test_sar.c'; then $(CYGPATH_W) 'ta_test_func/test_sar.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_sar.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_sar.Tpo $(DEPDIR)/ta_regtest-test_sar.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ta_test_func/test_sar.c' object='ta_regtest-test_sar.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_sar.obj `if test -f 'ta_test_func/test_sar.c'; then $(CYGPATH_W) 'ta_test_func/test_sar.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_sar.c'; fi`

ta_regtest-test_rsi.o: ta_test_func/test_rsi.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_rsi.o -MD -MP -MF $(DEPDIR)/ta_regtest-test_rsi.Tpo -c -o ta_regtest-test_rsi.o `test -f 'ta_test_func/test_rsi.c' || echo '$(srcdir)/'`ta_test_func/test_rsi.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_rsi.Tpo $(DEPDIR)/ta_regtest-test_rsi.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ta_test_func/test_rsi.c' object='ta_regtest-test_rsi.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_rsi.o `test -f 'ta_test_func/test_rsi.c' || echo '$(srcdir)/'`ta_test_func/test_rsi.c

ta_regtest-test_rsi.obj: ta_test_func/test_rsi.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_rsi.obj -MD -MP -MF $(DEPDIR)/ta_regtest-test_rsi.Tpo -c -o ta_regtest-test_rsi.obj `if test -f 'ta_test_func/test_rsi.c'; then $(CYGPATH_W) 'ta_test_func/test_rsi.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_rsi.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_rsi.Tpo $(DEPDIR)/ta_regtest-test_rsi.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ta_test_func/test_rsi.c' object='ta_regtest-test_rsi.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_rsi.obj `if test -f 'ta_test_func/test_rsi.c'; then $(CYGPATH_W) 'ta_test_func/test_rsi.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_rsi.c'; fi`

ta_regtest-test_candlestick.o: ta_test_func/test_candlestick.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_candlestick.o -MD -MP -MF $(DEPDIR)/ta_regtest-test_candlestick.Tpo -c -o ta_regtest-test_candlestick.o `test -f 'ta_test_func/test_candlestick.c' || echo '$(srcdir)/'`ta_test_func/test_candlestick.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_candlestick.Tpo $(DEPDIR)/ta_regtest-test_candlestick.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ta_test_func/test_candlestick.c' object='ta_regtest-test_candlestick.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_candlestick.o `test -f 'ta_test_func/test_candlestick.c' || echo '$(srcdir)/'`ta_test_func/test_candlestick.c

ta_regtest-test_candlestick.obj: ta_test_func/test_candlestick.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_candlestick.obj -MD -MP -MF $(DEPDIR)/ta_regtest-test_candlestick.Tpo -c -o ta_regtest-test_candlestick.obj `if test -f 'ta_test_func/test_candlestick.c'; then $(CYGPATH_W) 'ta_test_func/test_candlestick.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_candlestick.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_candlestick.Tpo $(DEPDIR)/ta_regtest-test_candlestick.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ta_test_func/test_candlestick.c' object='ta_regtest-test_candlestick.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_candlestick.obj `if test -f 'ta_test_func/test_candlestick.c'; then $(CYGPATH_W) 'ta_test_func/test_candlestick.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_candlestick.c'; fi`

ta_regtest-test_per_ema.o: ta_test_func/test_per_ema.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_per_ema.o -MD -MP -MF $(DEPDIR)/ta_regtest-test_per_ema.Tpo -c -o ta_regtest-test_per_ema.o `test -f 'ta_test_func/test_per_ema.c' || echo '$(srcdir)/'`ta_test_func/test_per_ema.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_per_ema.Tpo $(DEPDIR)/ta_regtest-test_per_ema.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ta_test_func/test_per_ema.c' object='ta_regtest-test_per_ema.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_per_ema.o `test -f 'ta_test_func/test_per_ema.c' || echo '$(srcdir)/'`ta_test_func/test_per_ema.c

ta_regtest-test_per_ema.obj: ta_test_func/test_per_ema.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_per_ema.obj -MD -MP -MF $(DEPDIR)/ta_regtest-test_per_ema.Tpo -c -o ta_regtest-test_per_ema.obj `if test -f 'ta_test_func/test_per_ema.c'; then $(CYGPATH_W) 'ta_test_func/test_per_ema.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_per_ema.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_per_ema.Tpo $(DEPDIR)/ta_regtest-test_per_ema.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ta_test_func/test_per_ema.c' object='ta_regtest-test_per_ema.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_per_ema.obj `if test -f 'ta_test_func/test_per_ema.c'; then $(CYGPATH_W) 'ta_test_func/test_per_ema.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_per_ema.c'; fi`

ta_regtest-test_per_hlc.o: ta_test_func/test_per_hlc.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_per_hlc.o -MD -MP -MF $(DEPDIR)/ta_regtest-test_per_hlc.Tpo -c -o ta_regtest-test_per_hlc.o `test -f 'ta_test_func/test_per_hlc.c' || echo '$(srcdir)/'`ta_test_func/test_per_hlc.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_per_hlc.Tpo $(DEPDIR)/ta_regtest-test_per_hlc.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ta_test_func/test_per_hlc.c' object='ta_regtest-test_per_hlc.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_per_hlc.o `test -f 'ta_test_func/test_per_hlc.c' || echo '$(srcdir)/'`ta_test_func/test_per_hlc.c

ta_regtest-test_per_hlc.obj: ta_test_func/test_per_hlc.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_per_hlc.obj -MD -MP -MF $(DEPDIR)/ta_regtest-test_per_hlc.Tpo -c -o ta_regtest-test_per_hlc.obj `if test -f 'ta_test_func/test_per_hlc.c'; then $(CYGPATH_W) 'ta_test_func/test_per_hlc.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_per_hlc.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_per_hlc.Tpo $(DEPDIR)/ta_regtest-test_per_hlc.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ta_test_func/test_per_hlc.c' object='ta_regtest-test_per_hlc.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_per_hlc.obj `if test -f 'ta_test_func/test_per_hlc.c'; then $(CYGPATH_W) 'ta_test_func/test_per_hlc.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_per_hlc.c'; fi`

ta_regtest-test_stoch.o: ta_test_func/test_stoch.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_stoch.o -MD -MP -MF $(DEPDIR)/ta_regtest-test_stoch.Tpo -c -o ta_regtest-test_stoch.o `test -f 'ta_test_func/test_stoch.c' || echo '$(srcdir)/'`ta_test_func/test_stoch.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_stoch.Tpo $(DEPDIR)/ta_regtest-test_stoch.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ta_test_func/test_stoch.c' object='ta_regtest-test_stoch.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_stoch.o `test -f 'ta_test_func/test_stoch.c' || echo '$(srcdir)/'`ta_test_func/test_stoch.c

ta_regtest-test_stoch.obj: ta_test_func/test_stoch.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_stoch.obj -MD -MP -MF $(DEPDIR)/ta_regtest-test_stoch.Tpo -c -o ta_regtest-test_stoch.obj `if test -f 'ta_test_func/test_stoch.c'; then $(CYGPATH_W) 'ta_test_func/test_stoch.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_stoch.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_stoch.Tpo $(DEPDIR)/ta_regtest-test_stoch.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ta_test_func/test_stoch.c' object='ta_regtest-test_stoch.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_stoch.obj `if test -f 'ta_test_func/test_stoch.c'; then $(CYGPATH_W) 'ta_test_func/test_stoch.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_stoch.c'; fi`

ta_regtest-test_macd.o: ta_test_func/test_macd.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_macd.o -MD -MP -MF $(DEPDIR)/ta_regtest-test_macd.Tpo -c -o ta_regtest-test_macd.o `test -f 'ta_test_func/test_macd.c' || echo '$(srcdir)/'`ta_test_func/test_macd.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_macd.Tpo $(DEPDIR)/ta_regtest-test_macd.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ta_test_func/test_macd.c' object='ta_regtest-test_macd.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_macd.o `test -f 'ta_test_func/test_macd.c' || echo '$(srcdir)/'`ta_test_func/test_macd.c

ta_regtest-test_macd.obj: ta_test_func/test_macd.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_macd.obj -MD -MP -MF $(DEPDIR)/ta_regtest-test_macd.Tpo -c -o ta_regtest-test_macd.obj `if test -f 'ta_test_func/test_macd.c'; then $(CYGPATH_W) 'ta_test_func/test_macd.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_macd.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_macd.Tpo $(DEPDIR)/ta_regtest-test_macd.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ta_test_func/test_macd.c' object='ta_regtest-test_macd.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_macd.obj `if test -f 'ta_test_func/test_macd.c'; then $(CYGPATH_W) 'ta_test_func/test_macd.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_macd.c'; fi`

ta_regtest-test_minmax.o: ta_test_func/test_minmax.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_minmax.o -MD -MP -MF $(DEPDIR)/ta_regtest-test_minmax.Tpo -c -o ta_regtest-test_minmax.o `test -f 'ta_test_func/test_minmax.c' || echo '$(srcdir)/'`ta_test_func/test_minmax.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_minmax.Tpo $(DEPDIR)/ta_regtest-test_minmax.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ta_test_func/test_minmax.c' object='ta_regtest-test_minmax.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_minmax.o `test -f 'ta_test_func/test_minmax.c' || echo '$(srcdir)/'`ta_test_func/test_minmax.c

ta_regtest-test_minmax.obj: ta_test_func/test_minmax.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_minmax.obj -MD -MP -MF $(DEPDIR)/ta_regtest-test_minmax.Tpo -c -o ta_regtest-test_minmax.obj `if test -f 'ta_test_func/test_minmax.c'; then $(CYGPATH_W) 'ta_test_func/test_minmax.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_minmax.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_minmax.Tpo $(DEPDIR)/ta_regtest-test_minmax.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ta_test_func/test_minmax.c' object='ta_regtest-test_minmax.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_minmax.obj `if test -f 'ta_test_func/test_minmax.c'; then $(CYGPATH_W) 'ta_test_func/test_minmax.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_minmax.c'; fi`

ta_regtest-test_per_hlcv.o: ta_test_func/test_per_hlcv.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_per_hlcv.o -MD -MP -MF $(DEPDIR)/ta_regtest-test_per_hlcv.Tpo -c -o ta_regtest-test_per_hlcv.o `test -f 'ta_test_func/test_per_hlcv.c' || echo '$(srcdir)/'`ta_test_func/test_per_hlcv.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_per_hlcv.Tpo $(DEPDIR)/ta_regtest-test_per_hlcv.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ta_test_func/test_per_hlcv.c' object='ta_regtest-test_per_hlcv.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_per_hlcv.o `test -f 'ta_test_func/test_per_hlcv.c' || echo '$(srcdir)/'`ta_test_func/test_per_hlcv.c

ta_regtest-test_per_hlcv.obj: ta_test_func/test_per_hlcv.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_per_hlcv.obj -MD -MP -MF $(DEPDIR)/ta_regtest-test_per_hlcv.Tpo -c -o ta_regtest-test_per_hlcv.obj `if test -f 'ta_test_func/test_per_hlcv.c'; then $(CYGPATH_W) 'ta_test_func/test_per_hlcv.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_per_hlcv.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_per_hlcv.Tpo $(DEPDIR)/ta_regtest-test_per_hlcv.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ta_test_func/test_per_hlcv.c' object='ta_regtest-test_per_hlcv.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_per_hlcv.obj `if test -f 'ta_test_func/test_per_hlcv.c'; then $(CYGPATH_W) 'ta_test_func/test_per_hlcv.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_per_hlcv.c'; fi`

ta_regtest-test_1in_1out.o: ta_test_func/test_1in_1out.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_1in_1out.o -MD -MP -MF $(DEPDIR)/ta_regtest-test_1in_1out.Tpo -c -o ta_regtest-test_1in_1out.o `test -f 'ta_test_func/test_1in_1out.c' || echo '$(srcdir)/'`ta_test_func/test_1in_1out.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_1in_1out.Tpo $(DEPDIR)/ta_regtest-test_1in_1out.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ta_test_func/test_1in_1out.c' object='ta_regtest-test_1in_1out.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_1in_1out.o `test -f 'ta_test_func/test_1in_1out.c' || echo '$(srcdir)/'`ta_test_func/test_1in_1out.c

ta_regtest-test_1in_1out.obj: ta_test_func/test_1in_1out.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_1in_1out.obj -MD -MP -MF $(DEPDIR)/ta_regtest-test_1in_1out.Tpo -c -o ta_regtest-test_1in_1out.obj `if test -f 'ta_test_func/test_1in_1out.c'; then $(CYGPATH_W) 'ta_test_func/test_1in_1out.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_1in_1out.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_1in_1out.Tpo $(DEPDIR)/ta_regtest-test_1in_1out.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ta_test_func/test_1in_1out.c' object='ta_regtest-test_1in_1out.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_1in_1out.obj `if test -f 'ta_test_func/test_1in_1out.c'; then $(CYGPATH_W) 'ta_test_func/test_1in_1out.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_1in_1out.c'; fi`

ta_regtest-test_1in_2out.o: ta_test_func/test_1in_2out.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_1in_2out.o -MD -MP -MF $(DEPDIR)/ta_regtest-test_1in_2out.Tpo -c -o ta_regtest-test_1in_2out.o `test -f 'ta_test_func/test_1in_2out.c' || echo '$(srcdir)/'`ta_test_func/test_1in_2out.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_1in_2out.Tpo $(DEPDIR)/ta_regtest-test_1in_2out.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ta_test_func/test_1in_2out.c' object='ta_regtest-test_1in_2out.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_1in_2out.o `test -f 'ta_test_func/test_1in_2out.c' || echo '$(srcdir)/'`ta_test_func/test_1in_2out.c

ta_regtest-test_1in_2out.obj: ta_test_func/test_1in_2out.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_1in_2out.obj -MD -MP -MF $(DEPDIR)/ta_regtest-test_1in_2out.Tpo -c -o ta_regtest-test_1in_2out.obj `if test -f 'ta_test_func/test_1in_2out.c'; then $(CYGPATH_W) 'ta_test_func/test_1in_2out.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_1in_2out.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_1in_2out.Tpo $(DEPDIR)/ta_regtest-test_1in_2out.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ta_test_func/test_1in_2out.c' object='ta_regtest-test_1in_2out.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_1in_2out.obj `if test -f 'ta_test_func/test_1in_2out.c'; then $(CYGPATH_W) 'ta_test_func/test_1in_2out.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_1in_2out.c'; fi`

ta_regtest-test_per_ohlc.o: ta_test_func/test_per_ohlc.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_per_ohlc.o -MD -MP -MF $(DEPDIR)/ta_regtest-test_per_ohlc.Tpo -c -o ta_regtest-test_per_ohlc.o `test -f 'ta_test_func/test_per_ohlc.c' || echo '$(srcdir)/'`ta_test_func/test_per_ohlc.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_per_ohlc.Tpo $(DEPDIR)/ta_regtest-test_per_ohlc.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ta_test_func/test_per_ohlc.c' object='ta_regtest-test_per_ohlc.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_per_ohlc.o `test -f 'ta_test_func/test_per_ohlc.c' || echo '$(srcdir)/'`ta_test_func/test_per_ohlc.c

ta_regtest-test_per_ohlc.obj: ta_test_func/test_per_ohlc.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_per_ohlc.obj -MD -MP -MF $(DEPDIR)/ta_regtest-test_per_ohlc.Tpo -c -o ta_regtest-test_per_ohlc.obj `if test -f 'ta_test_func/test_per_ohlc.c'; then $(CYGPATH_W) 'ta_test_func/test_per_ohlc.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_per_ohlc.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_per_ohlc.Tpo $(DEPDIR)/ta_regtest-test_per_ohlc.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ta_test_func/test_per_ohlc.c' object='ta_regtest-test_per_ohlc.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_per_ohlc.obj `if test -f 'ta_test_func/test_per_ohlc.c'; then $(CYGPATH_W) 'ta_test_func/test_per_ohlc.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_per_ohlc.c'; fi`

ta_regtest-test_stddev.o: ta_test_func/test_stddev.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_stddev.o -MD -MP -MF $(DEPDIR)/ta_regtest-test_stddev.Tpo -c -o ta_regtest-test_stddev.o `test -f 'ta_test_func/test_stddev.c' || echo '$(srcdir)/'`ta_test_func/test_stddev.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_stddev.Tpo $(DEPDIR)/ta_regtest-test_stddev.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ta_test_func/test_stddev.c' object='ta_regtest-test_stddev.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_stddev.o `test -f 'ta_test_func/test_stddev.c' || echo '$(srcdir)/'`ta_test_func/test_stddev.c

ta_regtest-test_stddev.obj: ta_test_func/test_stddev.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_stddev.obj -MD -MP -MF $(DEPDIR)/ta_regtest-test_stddev.Tpo -c -o ta_regtest-test_stddev.obj `if test -f 'ta_test_func/test_stddev.c'; then $(CYGPATH_W) 'ta_test_func/test_stddev.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_stddev.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_stddev.Tpo $(DEPDIR)/ta_regtest-test_stddev.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ta_test_func/test_stddev.c' object='ta_regtest-test_stddev.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_stddev.obj `if test -f 'ta_test_func/test_stddev.c'; then $(CYGPATH_W) 'ta_test_func/test_stddev.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_stddev.c'; fi`

ta_regtest-test_bbands.o: ta_test_func/test_bbands.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_bbands.o -MD -MP -MF $(DEPDIR)/ta_regtest-test_bbands.Tpo -c -o ta_regtest-test_bbands.o `test -f 'ta_test_func/test_bbands.c' || echo '$(srcdir)/'`ta_test_func/test_bbands.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_bbands.Tpo $(DEPDIR)/ta_regtest-test_bbands.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ta_test_func/test_bbands.c' object='ta_regtest-test_bbands.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_bbands.o `test -f 'ta_test_func/test_bbands.c' || echo '$(srcdir)/'`ta_test_func/test_bbands.c

ta_regtest-test_bbands.obj: ta_test_func/test_bbands.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_bbands.obj -MD -MP -MF $(DEPDIR)/ta_regtest-test_bbands.Tpo -c -o ta_regtest-test_bbands.obj `if test -f 'ta_test_func/test_bbands.c'; then $(CYGPATH_W) 'ta_test_func/test_bbands.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_bbands.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_bbands.Tpo $(DEPDIR)/ta_regtest-test_bbands.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ta_test_func/test_bbands.c' object='ta_regtest-test_bbands.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_bbands.obj `if test -f 'ta_test_func/test_bbands.c'; then $(CYGPATH_W) 'ta_test_func/test_bbands.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_bbands.c'; fi`

ta_regtest-test_ma.o: ta_test_func/test_ma.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_ma.o -MD -MP -MF $(DEPDIR)/ta_regtest-test_ma.Tpo -c -o ta_regtest-test_ma.o `test -f 'ta_test_func/test_ma.c' || echo '$(srcdir)/'`ta_test_func/test_ma.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_ma.Tpo $(DEPDIR)/ta_regtest-test_ma.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ta_test_func/test_ma.c' object='ta_regtest-test_ma.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_ma.o `test -f 'ta_test_func/test_ma.c' || echo '$(srcdir)/'`ta_test_func/test_ma.c

ta_regtest-test_ma.obj: ta_test_func/test_ma.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_ma.obj -MD -MP -MF $(DEPDIR)/ta_regtest-test_ma.Tpo -c -o ta_regtest-test_ma.obj `if test -f 'ta_test_func/test_ma.c'; then $(CYGPATH_W) 'ta_test_func/test_ma.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_ma.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_ma.Tpo $(DEPDIR)/ta_regtest-test_ma.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ta_test_func/test_ma.c' object='ta_regtest-test_ma.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_ma.obj `if test -f 'ta_test_func/test_ma.c'; then $(CYGPATH_W) 'ta_test_func/test_ma.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_ma.c'; fi`

ta_regtest-test_po.o: ta_test_func/test_po.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_po.o -MD -MP -MF $(DEPDIR)/ta_regtest-test_po.Tpo -c -o ta_regtest-test_po.o `test -f 'ta_test_func/test_po.c' || echo '$(srcdir)/'`ta_test_func/test_po.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_po.Tpo $(DEPDIR)/ta_regtest-test_po.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ta_test_func/test_po.c' object='ta_regtest-test_po.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_po.o `test -f 'ta_test_func/test_po.c' || echo '$(srcdir)/'`ta_test_func/test_po.c

ta_regtest-test_po.obj: ta_test_func/test_po.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_po.obj -MD -MP -MF $(DEPDIR)/ta_regtest-test_po.Tpo -c -o ta_regtest-test_po.obj `if test -f 'ta_test_func/test_po.c'; then $(CYGPATH_W) 'ta_test_func/test_po.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_po.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_po.Tpo $(DEPDIR)/ta_regtest-test_po.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ta_test_func/test_po.c' object='ta_regtest-test_po.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_po.obj `if test -f 'ta_test_func/test_po.c'; then $(CYGPATH_W) 'ta_test_func/test_po.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_po.c'; fi`

ta_regtest-test_per_hl.o: ta_test_func/test_per_hl.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_per_hl.o -MD -MP -MF $(DEPDIR)/ta_regtest-test_per_hl.Tpo -c -o ta_regtest-test_per_hl.o `test -f 'ta_test_func/test_per_hl.c' || echo '$(srcdir)/'`ta_test_func/test_per_hl.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_per_hl.Tpo $(DEPDIR)/ta_regtest-test_per_hl.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ta_test_func/test_per_hl.c' object='ta_regtest-test_per_hl.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_per_hl.o `test -f 'ta_test_func/test_per_hl.c' || echo '$(srcdir)/'`ta_test_func/test_per_hl.c

ta_regtest-test_per_hl.obj: ta_test_func/test_per_hl.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_per_hl.obj -MD -MP -MF $(DEPDIR)/ta_regtest-test_per_hl.Tpo -c -o ta_regtest-test_per_hl.obj `if test -f 'ta_test_func/test_per_hl.c'; then $(CYGPATH_W) 'ta_test_func/test_per_hl.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_per_hl.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_per_hl.Tpo $(DEPDIR)/ta_regtest-test_per_hl.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ta_test_func/test_per_hl.c' object='ta_regtest-test_per_hl.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_per_hl.obj `if test -f 'ta_test_func/test_per_hl.c'; then $(CYGPATH_W) 'ta_test_func/test_per_hl.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_per_hl.c'; fi`

ta_regtest-test_trange.o: ta_test_func/test_trange.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_trange.o -MD -MP -MF $(DEPDIR)/ta_regtest-test_trange.Tpo -c -o ta_regtest-test_trange.o `test -f 'ta_test_func/test_trange.c' || echo '$(srcdir)/'`ta_test_func/test_trange.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_trange.Tpo $(DEPDIR)/ta_regtest-test_trange.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ta_test_func/test_trange.c' object='ta_regtest-test_trange.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_trange.o `test -f 'ta_test_func/test_trange.c' || echo '$(srcdir)/'`ta_test_func/test_trange.c

ta_regtest-test_trange.obj: ta_test_func/test_trange.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_trange.obj -MD -MP -MF $(DEPDIR)/ta_regtest-test_trange.Tpo -c -o ta_regtest-test_trange.obj `if test -f 'ta_test_func/test_trange.c'; then $(CYGPATH_W) 'ta_test_func/test_trange.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_trange.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_trange.Tpo $(DEPDIR)/ta_regtest-test_trange.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='ta_test_func/test_trange.c' object='ta_regtest-test_trange.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_trange.obj `if test -f 'ta_test_func/test_trange.c'; then $(CYGPATH_W) 'ta_test_func/test_trange.c'; else $(CYGPATH_W) '$(srcdir)/ta_test_func/test_trange.c'; fi`

ta_regtest-test_internals.o: test_internals.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_internals.o -MD -MP -MF $(DEPDIR)/ta_regtest-test_internals.Tpo -c -o ta_regtest-test_internals.o `test -f 'test_internals.c' || echo '$(srcdir)/'`test_internals.c
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_internals.Tpo $(DEPDIR)/ta_regtest-test_internals.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='test_internals.c' object='ta_regtest-test_internals.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_internals.o `test -f 'test_internals.c' || echo '$(srcdir)/'`test_internals.c

ta_regtest-test_internals.obj: test_internals.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -MT ta_regtest-test_internals.obj -MD -MP -MF $(DEPDIR)/ta_regtest-test_internals.Tpo -c -o ta_regtest-test_internals.obj `if test -f 'test_internals.c'; then $(CYGPATH_W) 'test_internals.c'; else $(CYGPATH_W) '$(srcdir)/test_internals.c'; fi`
@am__fastdepCC_TRUE@	mv -f $(DEPDIR)/ta_regtest-test_internals.Tpo $(DEPDIR)/ta_regtest-test_internals.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='test_internals.c' object='ta_regtest-test_internals.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(ta_regtest_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS) -c -o ta_regtest-test_internals.obj `if test -f 'test_internals.c'; then $(CYGPATH_W) 'test_internals.c'; else $(CYGPATH_W) '$(srcdir)/test_internals.c'; fi`

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs

ID: $(HEADERS) $(SOURCES) $(LISP) $(TAGS_FILES)
	list='$(SOURCES) $(HEADERS) $(LISP) $(TAGS_FILES)'; \
	unique=`for i in $$list; do \
	    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
	  done | \
	  $(AWK) '    { files[$$0] = 1; } \
	       END { for (i in files) print i; }'`; \
	mkid -fID $$unique
tags: TAGS

TAGS:  $(HEADERS) $(SOURCES)  $(TAGS_DEPENDENCIES) \
		$(TAGS_FILES) $(LISP)
	tags=; \
	here=`pwd`; \
	list='$(SOURCES) $(HEADERS)  $(LISP) $(TAGS_FILES)'; \
	unique=`for i in $$list; do \
	    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
	  done | \
	  $(AWK) '    { files[$$0] = 1; } \
	       END { for (i in files) print i; }'`; \
	if test -z "$(ETAGS_ARGS)$$tags$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	    $$tags $$unique; \
	fi
ctags: CTAGS
CTAGS:  $(HEADERS) $(SOURCES)  $(TAGS_DEPENDENCIES) \
		$(TAGS_FILES) $(LISP)
	tags=; \
	here=`pwd`; \
	list='$(SOURCES) $(HEADERS)  $(LISP) $(TAGS_FILES)'; \
	unique=`for i in $$list; do \
	    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
	  done | \
	  $(AWK) '    { files[$$0] = 1; } \
	       END { for (i in files) print i; }'`; \
	test -z "$(CTAGS_ARGS)$$tags$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$tags $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && cd $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) $$here

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags

distdir: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -pR $(srcdir)/$$file $(distdir)$$dir || exit 1; \
	    fi; \
	    cp -pR $$d/$$file $(distdir)$$dir || exit 1; \
	  else \
	    test -f $(distdir)/$$file \
	    || cp -p $$d/$$file $(distdir)/$$file \
	    || exit 1; \
	  fi; \
	done
check-am: all-am
check: check-am
all-am: Makefile $(PROGRAMS)
installdirs:
install: install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	$(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	  install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	  `test -z '$(STRIP)' || \
	    echo "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'"` install
mostlyclean-generic:

clean-generic:

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
clean: clean-am

clean-am: clean-generic clean-libtool clean-noinstPROGRAMS \
	mostlyclean-am

distclean: distclean-am
	-rm -rf ./$(DEPDIR)
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-tags

dvi: dvi-am

dvi-am:

html: html-am

info: info-am

info-am:

install-data-am:

install-dvi: install-dvi-am

install-exec-am:

install-html: install-html-am

install-info: install-info-am

install-man:

install-pdf: install-pdf-am

install-ps: install-ps-am

installcheck-am:

maintainer-clean: maintainer-clean-am
	-rm -rf ./$(DEPDIR)
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am:

.MAKE: install-am install-strip

.PHONY: CTAGS GTAGS all all-am check check-am clean clean-generic \
	clean-libtool clean-noinstPROGRAMS ctags distclean \
	distclean-compile distclean-generic distclean-libtool \
	distclean-tags distdir dvi dvi-am html html-am info info-am \
	install install-am install-data install-data-am install-dvi \
	install-dvi-am install-exec install-exec-am install-html \
	install-html-am install-info install-info-am install-man \
	install-pdf install-pdf-am install-ps install-ps-am \
	install-strip installcheck installcheck-am installdirs \
	maintainer-clean maintainer-clean-generic mostlyclean \
	mostlyclean-compile mostlyclean-generic mostlyclean-libtool \
	pdf pdf-am ps ps-am tags uninstall uninstall-am

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:
