
noinst_LTLIBRARIES = libta_func.la
AM_CPPFLAGS = -I../ta_common/

libta_func_la_SOURCES = ta_utility.c \
	ta_ACOS.c \
	ta_AD.c \
	ta_ADD.c \
	ta_ADOSC.c \
	ta_ADX.c \
	ta_ADXR.c \
	ta_APO.c \
	ta_AROON.c \
	ta_AROONOSC.c \
	ta_ASIN.c \
	ta_ATAN.c \
	ta_ATR.c \
	ta_AVGPRICE.c \
	ta_BBANDS.c \
	ta_BETA.c \
	ta_BOP.c \
	ta_CCI.c \
	ta_CDL2CROWS.c \
	ta_CDL3BLACKCROWS.c \
	ta_CDL3INSIDE.c \
	ta_CDL3LINESTRIKE.c \
	ta_CDL3OUTSIDE.c \
	ta_CDL3STARSINSOUTH.c \
	ta_CDL3WHITESOLDIERS.c \
	ta_CDLABANDONEDBABY.c \
	ta_CDLADVANCEBLOCK.c \
	ta_CDLBELTHOLD.c \
	ta_CDLBREAKAWAY.c \
	ta_CDLCLOSINGMARUBOZU.c \
	ta_CDLCONCEALBABYSWALL.c \
	ta_CDLCOUNTERATTACK.c \
	ta_CDLDARKCLOUDCOVER.c \
	ta_CDLDOJI.c \
	ta_CDLDOJISTAR.c \
	ta_CDLDRAGONFLYDOJI.c \
	ta_CDLENGULFING.c \
	ta_CDLEVENINGDOJISTAR.c \
	ta_CDLEVENINGSTAR.c \
	ta_CDLGAPSIDESIDEWHITE.c \
	ta_CDLGRAVESTONEDOJI.c \
	ta_CDLHAMMER.c \
	ta_CDLHANGINGMAN.c \
	ta_CDLHARAMI.c \
	ta_CDLHARAMICROSS.c \
	ta_CDLHIGHWAVE.c \
	ta_CDLHIKKAKE.c \
	ta_CDLHIKKAKEMOD.c \
	ta_CDLHOMINGPIGEON.c \
	ta_CDLIDENTICAL3CROWS.c \
	ta_CDLINNECK.c \
	ta_CDLINVERTEDHAMMER.c \
	ta_CDLKICKING.c \
	ta_CDLKICKINGBYLENGTH.c \
	ta_CDLLADDERBOTTOM.c \
	ta_CDLLONGLEGGEDDOJI.c \
	ta_CDLLONGLINE.c \
	ta_CDLMARUBOZU.c \
	ta_CDLMATCHINGLOW.c \
	ta_CDLMATHOLD.c \
	ta_CDLMORNINGDOJISTAR.c \
	ta_CDLMORNINGSTAR.c \
	ta_CDLONNECK.c \
	ta_CDLPIERCING.c \
	ta_CDLRICKSHAWMAN.c \
	ta_CDLRISEFALL3METHODS.c \
	ta_CDLSEPARATINGLINES.c \
	ta_CDLSHOOTINGSTAR.c \
	ta_CDLSHORTLINE.c \
	ta_CDLSPINNINGTOP.c \
	ta_CDLSTALLEDPATTERN.c \
	ta_CDLSTICKSANDWICH.c \
	ta_CDLTAKURI.c \
	ta_CDLTASUKIGAP.c \
	ta_CDLTHRUSTING.c \
	ta_CDLTRISTAR.c \
	ta_CDLUNIQUE3RIVER.c \
	ta_CDLUPSIDEGAP2CROWS.c \
	ta_CDLXSIDEGAP3METHODS.c \
	ta_CEIL.c \
	ta_CMO.c \
	ta_CORREL.c \
	ta_COS.c \
	ta_COSH.c \
	ta_DEMA.c \
	ta_DIV.c \
	ta_DX.c \
	ta_EMA.c \
	ta_EXP.c \
	ta_FLOOR.c \
	ta_HT_DCPERIOD.c \
	ta_HT_DCPHASE.c \
	ta_HT_PHASOR.c \
	ta_HT_SINE.c \
	ta_HT_TRENDLINE.c \
	ta_HT_TRENDMODE.c \
	ta_KAMA.c \
	ta_LINEARREG.c \
	ta_LINEARREG_ANGLE.c \
	ta_LINEARREG_INTERCEPT.c \
	ta_LINEARREG_SLOPE.c \
	ta_LN.c \
	ta_LOG10.c \
	ta_MA.c \
	ta_MACD.c \
	ta_MACDEXT.c \
	ta_MACDFIX.c \
	ta_MAMA.c \
	ta_MAVP.c \
	ta_MAX.c \
	ta_MAXINDEX.c \
	ta_MEDPRICE.c \
	ta_MFI.c \
	ta_MIDPOINT.c \
	ta_MIDPRICE.c \
	ta_MIN.c \
	ta_MININDEX.c \
	ta_MINMAX.c \
	ta_MINMAXINDEX.c \
	ta_MINUS_DI.c \
	ta_MINUS_DM.c \
	ta_MOM.c \
	ta_MULT.c \
	ta_NATR.c \
	ta_OBV.c \
	ta_PLUS_DI.c \
	ta_PLUS_DM.c \
	ta_PPO.c \
	ta_ROC.c \
	ta_ROCP.c \
	ta_ROCR.c \
	ta_ROCR100.c \
	ta_RSI.c \
	ta_SAR.c \
	ta_SAREXT.c \
	ta_SIN.c \
	ta_SINH.c \
	ta_SMA.c \
	ta_SQRT.c \
	ta_STDDEV.c \
	ta_STOCH.c \
	ta_STOCHF.c \
	ta_STOCHRSI.c \
	ta_SUB.c \
	ta_SUM.c \
	ta_T3.c \
	ta_TAN.c \
	ta_TANH.c \
	ta_TEMA.c \
	ta_TRANGE.c \
	ta_TRIMA.c \
	ta_TRIX.c \
	ta_TSF.c \
	ta_TYPPRICE.c \
	ta_ULTOSC.c \
	ta_VAR.c \
	ta_WCLPRICE.c \
	ta_WILLR.c \
	ta_WMA.c

libta_func_la_LDFLAGS = -version-info $(TALIB_LIBRARY_VERSION)

libta_funcdir=$(includedir)/ta-lib/
libta_func_HEADERS = ../../include/ta_defs.h \
	../../include/ta_libc.h \
	../../include/ta_func.h
