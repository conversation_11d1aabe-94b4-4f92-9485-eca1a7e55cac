# Makefile.in generated by automake 1.10 from Makefile.am.
# src/ta_func/Makefile.  Generated from Makefile.in by configure.

# Copyright (C) 1994, 1995, 1996, 1997, 1998, 1999, 2000, 2001, 2002,
# 2003, 2004, 2005, 2006  Free Software Foundation, Inc.
# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.





pkgdatadir = $(datadir)/ta-lib
pkglibdir = $(libdir)/ta-lib
pkgincludedir = $(includedir)/ta-lib
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = x86_64-unknown-linux-gnu
host_triplet = x86_64-unknown-linux-gnu
subdir = src/ta_func
DIST_COMMON = $(libta_func_HEADERS) $(srcdir)/Makefile.am \
	$(srcdir)/Makefile.in
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/configure.in
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/include/ta_config.h
CONFIG_CLEAN_FILES =
LTLIBRARIES = $(noinst_LTLIBRARIES)
libta_func_la_LIBADD =
am_libta_func_la_OBJECTS = ta_utility.lo ta_ACOS.lo ta_AD.lo ta_ADD.lo \
	ta_ADOSC.lo ta_ADX.lo ta_ADXR.lo ta_APO.lo ta_AROON.lo \
	ta_AROONOSC.lo ta_ASIN.lo ta_ATAN.lo ta_ATR.lo ta_AVGPRICE.lo \
	ta_BBANDS.lo ta_BETA.lo ta_BOP.lo ta_CCI.lo ta_CDL2CROWS.lo \
	ta_CDL3BLACKCROWS.lo ta_CDL3INSIDE.lo ta_CDL3LINESTRIKE.lo \
	ta_CDL3OUTSIDE.lo ta_CDL3STARSINSOUTH.lo \
	ta_CDL3WHITESOLDIERS.lo ta_CDLABANDONEDBABY.lo \
	ta_CDLADVANCEBLOCK.lo ta_CDLBELTHOLD.lo ta_CDLBREAKAWAY.lo \
	ta_CDLCLOSINGMARUBOZU.lo ta_CDLCONCEALBABYSWALL.lo \
	ta_CDLCOUNTERATTACK.lo ta_CDLDARKCLOUDCOVER.lo ta_CDLDOJI.lo \
	ta_CDLDOJISTAR.lo ta_CDLDRAGONFLYDOJI.lo ta_CDLENGULFING.lo \
	ta_CDLEVENINGDOJISTAR.lo ta_CDLEVENINGSTAR.lo \
	ta_CDLGAPSIDESIDEWHITE.lo ta_CDLGRAVESTONEDOJI.lo \
	ta_CDLHAMMER.lo ta_CDLHANGINGMAN.lo ta_CDLHARAMI.lo \
	ta_CDLHARAMICROSS.lo ta_CDLHIGHWAVE.lo ta_CDLHIKKAKE.lo \
	ta_CDLHIKKAKEMOD.lo ta_CDLHOMINGPIGEON.lo \
	ta_CDLIDENTICAL3CROWS.lo ta_CDLINNECK.lo \
	ta_CDLINVERTEDHAMMER.lo ta_CDLKICKING.lo \
	ta_CDLKICKINGBYLENGTH.lo ta_CDLLADDERBOTTOM.lo \
	ta_CDLLONGLEGGEDDOJI.lo ta_CDLLONGLINE.lo ta_CDLMARUBOZU.lo \
	ta_CDLMATCHINGLOW.lo ta_CDLMATHOLD.lo ta_CDLMORNINGDOJISTAR.lo \
	ta_CDLMORNINGSTAR.lo ta_CDLONNECK.lo ta_CDLPIERCING.lo \
	ta_CDLRICKSHAWMAN.lo ta_CDLRISEFALL3METHODS.lo \
	ta_CDLSEPARATINGLINES.lo ta_CDLSHOOTINGSTAR.lo \
	ta_CDLSHORTLINE.lo ta_CDLSPINNINGTOP.lo \
	ta_CDLSTALLEDPATTERN.lo ta_CDLSTICKSANDWICH.lo ta_CDLTAKURI.lo \
	ta_CDLTASUKIGAP.lo ta_CDLTHRUSTING.lo ta_CDLTRISTAR.lo \
	ta_CDLUNIQUE3RIVER.lo ta_CDLUPSIDEGAP2CROWS.lo \
	ta_CDLXSIDEGAP3METHODS.lo ta_CEIL.lo ta_CMO.lo ta_CORREL.lo \
	ta_COS.lo ta_COSH.lo ta_DEMA.lo ta_DIV.lo ta_DX.lo ta_EMA.lo \
	ta_EXP.lo ta_FLOOR.lo ta_HT_DCPERIOD.lo ta_HT_DCPHASE.lo \
	ta_HT_PHASOR.lo ta_HT_SINE.lo ta_HT_TRENDLINE.lo \
	ta_HT_TRENDMODE.lo ta_KAMA.lo ta_LINEARREG.lo \
	ta_LINEARREG_ANGLE.lo ta_LINEARREG_INTERCEPT.lo \
	ta_LINEARREG_SLOPE.lo ta_LN.lo ta_LOG10.lo ta_MA.lo ta_MACD.lo \
	ta_MACDEXT.lo ta_MACDFIX.lo ta_MAMA.lo ta_MAVP.lo ta_MAX.lo \
	ta_MAXINDEX.lo ta_MEDPRICE.lo ta_MFI.lo ta_MIDPOINT.lo \
	ta_MIDPRICE.lo ta_MIN.lo ta_MININDEX.lo ta_MINMAX.lo \
	ta_MINMAXINDEX.lo ta_MINUS_DI.lo ta_MINUS_DM.lo ta_MOM.lo \
	ta_MULT.lo ta_NATR.lo ta_OBV.lo ta_PLUS_DI.lo ta_PLUS_DM.lo \
	ta_PPO.lo ta_ROC.lo ta_ROCP.lo ta_ROCR.lo ta_ROCR100.lo \
	ta_RSI.lo ta_SAR.lo ta_SAREXT.lo ta_SIN.lo ta_SINH.lo \
	ta_SMA.lo ta_SQRT.lo ta_STDDEV.lo ta_STOCH.lo ta_STOCHF.lo \
	ta_STOCHRSI.lo ta_SUB.lo ta_SUM.lo ta_T3.lo ta_TAN.lo \
	ta_TANH.lo ta_TEMA.lo ta_TRANGE.lo ta_TRIMA.lo ta_TRIX.lo \
	ta_TSF.lo ta_TYPPRICE.lo ta_ULTOSC.lo ta_VAR.lo ta_WCLPRICE.lo \
	ta_WILLR.lo ta_WMA.lo
libta_func_la_OBJECTS = $(am_libta_func_la_OBJECTS)
libta_func_la_LINK = $(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(libta_func_la_LDFLAGS) $(LDFLAGS) -o $@
DEFAULT_INCLUDES = -I. -I$(top_builddir)/include
depcomp = $(SHELL) $(top_srcdir)/depcomp
am__depfiles_maybe = depfiles
COMPILE = $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) \
	$(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
LTCOMPILE = $(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) \
	--mode=compile $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) \
	$(AM_CPPFLAGS) $(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
CCLD = $(CC)
LINK = $(LIBTOOL) --tag=CC $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) \
	--mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) $(AM_LDFLAGS) \
	$(LDFLAGS) -o $@
SOURCES = $(libta_func_la_SOURCES)
DIST_SOURCES = $(libta_func_la_SOURCES)
am__vpath_adj_setup = srcdirstrip=`echo "$(srcdir)" | sed 's|.|.|g'`;
am__vpath_adj = case $$p in \
    $(srcdir)/*) f=`echo "$$p" | sed "s|^$$srcdirstrip/||"`;; \
    *) f=$$p;; \
  esac;
am__strip_dir = `echo $$p | sed -e 's|^.*/||'`;
am__installdirs = "$(DESTDIR)$(libta_funcdir)"
libta_funcHEADERS_INSTALL = $(INSTALL_HEADER)
HEADERS = $(libta_func_HEADERS)
ETAGS = etags
CTAGS = ctags
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
ACLOCAL = ${SHELL} /home/<USER>/Algo-trade/live-data-fetching/ta-lib/missing --run aclocal-1.10
AMTAR = ${SHELL} /home/<USER>/Algo-trade/live-data-fetching/ta-lib/missing --run tar
AR = ar
AUTOCONF = ${SHELL} /home/<USER>/Algo-trade/live-data-fetching/ta-lib/missing --run autoconf
AUTOHEADER = ${SHELL} /home/<USER>/Algo-trade/live-data-fetching/ta-lib/missing --run autoheader
AUTOMAKE = ${SHELL} /home/<USER>/Algo-trade/live-data-fetching/ta-lib/missing --run automake-1.10
AWK = mawk
CC = gcc
CCDEPMODE = depmode=gcc3
CFLAGS = -g -O2
CPP = gcc -E
CPPFLAGS = 
CXX = g++
CXXCPP = g++ -E
CXXDEPMODE = depmode=gcc3
CXXFLAGS = -g -O2
CYGPATH_W = echo
DEFS = -DHAVE_CONFIG_H
DEPDIR = .deps
ECHO = echo
ECHO_C = 
ECHO_N = -n
ECHO_T = 
EGREP = /usr/bin/grep -E
EXEEXT = 
F77 = 
FFLAGS = 
GREP = /usr/bin/grep
INSTALL = /usr/bin/install -c
INSTALL_DATA = ${INSTALL} -m 644
INSTALL_PROGRAM = ${INSTALL}
INSTALL_SCRIPT = ${INSTALL}
INSTALL_STRIP_PROGRAM = $(install_sh) -c -s
LDFLAGS = 
LIBOBJS = 
LIBS = -lpthread -ldl 
LIBTOOL = $(SHELL) $(top_builddir)/libtool
LN_S = ln -s
LTLIBOBJS = 
MAKEINFO = ${SHELL} /home/<USER>/Algo-trade/live-data-fetching/ta-lib/missing --run makeinfo
MKDIR_P = /usr/bin/mkdir -p
OBJEXT = o
PACKAGE = ta-lib
PACKAGE_BUGREPORT = http://sourceforge.net/tracker/?group_id=8903&atid=108903
PACKAGE_NAME = ta-lib
PACKAGE_STRING = ta-lib 0.4.0
PACKAGE_TARNAME = ta-lib
PACKAGE_VERSION = 0.4.0
PATH_SEPARATOR = :
POW_LIB = 
RANLIB = ranlib
SET_MAKE = 
SHELL = /bin/bash
STRIP = strip
TALIB_LIBRARY_VERSION = 0:0:0
VERSION = 0.4.0
abs_builddir = /home/<USER>/Algo-trade/live-data-fetching/ta-lib/src/ta_func
abs_srcdir = /home/<USER>/Algo-trade/live-data-fetching/ta-lib/src/ta_func
abs_top_builddir = /home/<USER>/Algo-trade/live-data-fetching/ta-lib
abs_top_srcdir = /home/<USER>/Algo-trade/live-data-fetching/ta-lib
ac_ct_CC = gcc
ac_ct_CXX = g++
ac_ct_F77 = 
am__include = include
am__leading_dot = .
am__quote = 
am__tar = ${AMTAR} chof - "$$tardir"
am__untar = ${AMTAR} xf -
bindir = ${exec_prefix}/bin
build = x86_64-unknown-linux-gnu
build_alias = 
build_cpu = x86_64
build_os = linux-gnu
build_vendor = unknown
builddir = .
datadir = ${datarootdir}
datarootdir = ${prefix}/share
docdir = ${datarootdir}/doc/${PACKAGE_TARNAME}
dvidir = ${docdir}
exec_prefix = ${prefix}
host = x86_64-unknown-linux-gnu
host_alias = 
host_cpu = x86_64
host_os = linux-gnu
host_vendor = unknown
htmldir = ${docdir}
includedir = ${prefix}/include
infodir = ${datarootdir}/info
install_sh = $(SHELL) /home/<USER>/Algo-trade/live-data-fetching/ta-lib/install-sh
libdir = ${exec_prefix}/lib
libexecdir = ${exec_prefix}/libexec
localedir = ${datarootdir}/locale
localstatedir = ${prefix}/var
mandir = ${datarootdir}/man
mkdir_p = /usr/bin/mkdir -p
oldincludedir = /usr/include
pdfdir = ${docdir}
prefix = /usr/local
program_transform_name = s,x,x,
psdir = ${docdir}
sbindir = ${exec_prefix}/sbin
sharedstatedir = ${prefix}/com
srcdir = .
sysconfdir = ${prefix}/etc
target_alias = 
top_builddir = ../..
top_srcdir = ../..
noinst_LTLIBRARIES = libta_func.la
AM_CPPFLAGS = -I../ta_common/
libta_func_la_SOURCES = ta_utility.c \
	ta_ACOS.c \
	ta_AD.c \
	ta_ADD.c \
	ta_ADOSC.c \
	ta_ADX.c \
	ta_ADXR.c \
	ta_APO.c \
	ta_AROON.c \
	ta_AROONOSC.c \
	ta_ASIN.c \
	ta_ATAN.c \
	ta_ATR.c \
	ta_AVGPRICE.c \
	ta_BBANDS.c \
	ta_BETA.c \
	ta_BOP.c \
	ta_CCI.c \
	ta_CDL2CROWS.c \
	ta_CDL3BLACKCROWS.c \
	ta_CDL3INSIDE.c \
	ta_CDL3LINESTRIKE.c \
	ta_CDL3OUTSIDE.c \
	ta_CDL3STARSINSOUTH.c \
	ta_CDL3WHITESOLDIERS.c \
	ta_CDLABANDONEDBABY.c \
	ta_CDLADVANCEBLOCK.c \
	ta_CDLBELTHOLD.c \
	ta_CDLBREAKAWAY.c \
	ta_CDLCLOSINGMARUBOZU.c \
	ta_CDLCONCEALBABYSWALL.c \
	ta_CDLCOUNTERATTACK.c \
	ta_CDLDARKCLOUDCOVER.c \
	ta_CDLDOJI.c \
	ta_CDLDOJISTAR.c \
	ta_CDLDRAGONFLYDOJI.c \
	ta_CDLENGULFING.c \
	ta_CDLEVENINGDOJISTAR.c \
	ta_CDLEVENINGSTAR.c \
	ta_CDLGAPSIDESIDEWHITE.c \
	ta_CDLGRAVESTONEDOJI.c \
	ta_CDLHAMMER.c \
	ta_CDLHANGINGMAN.c \
	ta_CDLHARAMI.c \
	ta_CDLHARAMICROSS.c \
	ta_CDLHIGHWAVE.c \
	ta_CDLHIKKAKE.c \
	ta_CDLHIKKAKEMOD.c \
	ta_CDLHOMINGPIGEON.c \
	ta_CDLIDENTICAL3CROWS.c \
	ta_CDLINNECK.c \
	ta_CDLINVERTEDHAMMER.c \
	ta_CDLKICKING.c \
	ta_CDLKICKINGBYLENGTH.c \
	ta_CDLLADDERBOTTOM.c \
	ta_CDLLONGLEGGEDDOJI.c \
	ta_CDLLONGLINE.c \
	ta_CDLMARUBOZU.c \
	ta_CDLMATCHINGLOW.c \
	ta_CDLMATHOLD.c \
	ta_CDLMORNINGDOJISTAR.c \
	ta_CDLMORNINGSTAR.c \
	ta_CDLONNECK.c \
	ta_CDLPIERCING.c \
	ta_CDLRICKSHAWMAN.c \
	ta_CDLRISEFALL3METHODS.c \
	ta_CDLSEPARATINGLINES.c \
	ta_CDLSHOOTINGSTAR.c \
	ta_CDLSHORTLINE.c \
	ta_CDLSPINNINGTOP.c \
	ta_CDLSTALLEDPATTERN.c \
	ta_CDLSTICKSANDWICH.c \
	ta_CDLTAKURI.c \
	ta_CDLTASUKIGAP.c \
	ta_CDLTHRUSTING.c \
	ta_CDLTRISTAR.c \
	ta_CDLUNIQUE3RIVER.c \
	ta_CDLUPSIDEGAP2CROWS.c \
	ta_CDLXSIDEGAP3METHODS.c \
	ta_CEIL.c \
	ta_CMO.c \
	ta_CORREL.c \
	ta_COS.c \
	ta_COSH.c \
	ta_DEMA.c \
	ta_DIV.c \
	ta_DX.c \
	ta_EMA.c \
	ta_EXP.c \
	ta_FLOOR.c \
	ta_HT_DCPERIOD.c \
	ta_HT_DCPHASE.c \
	ta_HT_PHASOR.c \
	ta_HT_SINE.c \
	ta_HT_TRENDLINE.c \
	ta_HT_TRENDMODE.c \
	ta_KAMA.c \
	ta_LINEARREG.c \
	ta_LINEARREG_ANGLE.c \
	ta_LINEARREG_INTERCEPT.c \
	ta_LINEARREG_SLOPE.c \
	ta_LN.c \
	ta_LOG10.c \
	ta_MA.c \
	ta_MACD.c \
	ta_MACDEXT.c \
	ta_MACDFIX.c \
	ta_MAMA.c \
	ta_MAVP.c \
	ta_MAX.c \
	ta_MAXINDEX.c \
	ta_MEDPRICE.c \
	ta_MFI.c \
	ta_MIDPOINT.c \
	ta_MIDPRICE.c \
	ta_MIN.c \
	ta_MININDEX.c \
	ta_MINMAX.c \
	ta_MINMAXINDEX.c \
	ta_MINUS_DI.c \
	ta_MINUS_DM.c \
	ta_MOM.c \
	ta_MULT.c \
	ta_NATR.c \
	ta_OBV.c \
	ta_PLUS_DI.c \
	ta_PLUS_DM.c \
	ta_PPO.c \
	ta_ROC.c \
	ta_ROCP.c \
	ta_ROCR.c \
	ta_ROCR100.c \
	ta_RSI.c \
	ta_SAR.c \
	ta_SAREXT.c \
	ta_SIN.c \
	ta_SINH.c \
	ta_SMA.c \
	ta_SQRT.c \
	ta_STDDEV.c \
	ta_STOCH.c \
	ta_STOCHF.c \
	ta_STOCHRSI.c \
	ta_SUB.c \
	ta_SUM.c \
	ta_T3.c \
	ta_TAN.c \
	ta_TANH.c \
	ta_TEMA.c \
	ta_TRANGE.c \
	ta_TRIMA.c \
	ta_TRIX.c \
	ta_TSF.c \
	ta_TYPPRICE.c \
	ta_ULTOSC.c \
	ta_VAR.c \
	ta_WCLPRICE.c \
	ta_WILLR.c \
	ta_WMA.c

libta_func_la_LDFLAGS = -version-info $(TALIB_LIBRARY_VERSION)
libta_funcdir = $(includedir)/ta-lib/
libta_func_HEADERS = ../../include/ta_defs.h \
	../../include/ta_libc.h \
	../../include/ta_func.h

all: all-am

.SUFFIXES:
.SUFFIXES: .c .lo .o .obj
$(srcdir)/Makefile.in:  $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh \
		&& exit 0; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --gnu  src/ta_func/Makefile'; \
	cd $(top_srcdir) && \
	  $(AUTOMAKE) --gnu  src/ta_func/Makefile
.PRECIOUS: Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__depfiles_maybe)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__depfiles_maybe);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure:  $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4):  $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

clean-noinstLTLIBRARIES:
	-test -z "$(noinst_LTLIBRARIES)" || rm -f $(noinst_LTLIBRARIES)
	@list='$(noinst_LTLIBRARIES)'; for p in $$list; do \
	  dir="`echo $$p | sed -e 's|/[^/]*$$||'`"; \
	  test "$$dir" != "$$p" || dir=.; \
	  echo "rm -f \"$${dir}/so_locations\""; \
	  rm -f "$${dir}/so_locations"; \
	done
libta_func.la: $(libta_func_la_OBJECTS) $(libta_func_la_DEPENDENCIES) 
	$(libta_func_la_LINK)  $(libta_func_la_OBJECTS) $(libta_func_la_LIBADD) $(LIBS)

mostlyclean-compile:
	-rm -f *.$(OBJEXT)

distclean-compile:
	-rm -f *.tab.c

include ./$(DEPDIR)/ta_ACOS.Plo
include ./$(DEPDIR)/ta_AD.Plo
include ./$(DEPDIR)/ta_ADD.Plo
include ./$(DEPDIR)/ta_ADOSC.Plo
include ./$(DEPDIR)/ta_ADX.Plo
include ./$(DEPDIR)/ta_ADXR.Plo
include ./$(DEPDIR)/ta_APO.Plo
include ./$(DEPDIR)/ta_AROON.Plo
include ./$(DEPDIR)/ta_AROONOSC.Plo
include ./$(DEPDIR)/ta_ASIN.Plo
include ./$(DEPDIR)/ta_ATAN.Plo
include ./$(DEPDIR)/ta_ATR.Plo
include ./$(DEPDIR)/ta_AVGPRICE.Plo
include ./$(DEPDIR)/ta_BBANDS.Plo
include ./$(DEPDIR)/ta_BETA.Plo
include ./$(DEPDIR)/ta_BOP.Plo
include ./$(DEPDIR)/ta_CCI.Plo
include ./$(DEPDIR)/ta_CDL2CROWS.Plo
include ./$(DEPDIR)/ta_CDL3BLACKCROWS.Plo
include ./$(DEPDIR)/ta_CDL3INSIDE.Plo
include ./$(DEPDIR)/ta_CDL3LINESTRIKE.Plo
include ./$(DEPDIR)/ta_CDL3OUTSIDE.Plo
include ./$(DEPDIR)/ta_CDL3STARSINSOUTH.Plo
include ./$(DEPDIR)/ta_CDL3WHITESOLDIERS.Plo
include ./$(DEPDIR)/ta_CDLABANDONEDBABY.Plo
include ./$(DEPDIR)/ta_CDLADVANCEBLOCK.Plo
include ./$(DEPDIR)/ta_CDLBELTHOLD.Plo
include ./$(DEPDIR)/ta_CDLBREAKAWAY.Plo
include ./$(DEPDIR)/ta_CDLCLOSINGMARUBOZU.Plo
include ./$(DEPDIR)/ta_CDLCONCEALBABYSWALL.Plo
include ./$(DEPDIR)/ta_CDLCOUNTERATTACK.Plo
include ./$(DEPDIR)/ta_CDLDARKCLOUDCOVER.Plo
include ./$(DEPDIR)/ta_CDLDOJI.Plo
include ./$(DEPDIR)/ta_CDLDOJISTAR.Plo
include ./$(DEPDIR)/ta_CDLDRAGONFLYDOJI.Plo
include ./$(DEPDIR)/ta_CDLENGULFING.Plo
include ./$(DEPDIR)/ta_CDLEVENINGDOJISTAR.Plo
include ./$(DEPDIR)/ta_CDLEVENINGSTAR.Plo
include ./$(DEPDIR)/ta_CDLGAPSIDESIDEWHITE.Plo
include ./$(DEPDIR)/ta_CDLGRAVESTONEDOJI.Plo
include ./$(DEPDIR)/ta_CDLHAMMER.Plo
include ./$(DEPDIR)/ta_CDLHANGINGMAN.Plo
include ./$(DEPDIR)/ta_CDLHARAMI.Plo
include ./$(DEPDIR)/ta_CDLHARAMICROSS.Plo
include ./$(DEPDIR)/ta_CDLHIGHWAVE.Plo
include ./$(DEPDIR)/ta_CDLHIKKAKE.Plo
include ./$(DEPDIR)/ta_CDLHIKKAKEMOD.Plo
include ./$(DEPDIR)/ta_CDLHOMINGPIGEON.Plo
include ./$(DEPDIR)/ta_CDLIDENTICAL3CROWS.Plo
include ./$(DEPDIR)/ta_CDLINNECK.Plo
include ./$(DEPDIR)/ta_CDLINVERTEDHAMMER.Plo
include ./$(DEPDIR)/ta_CDLKICKING.Plo
include ./$(DEPDIR)/ta_CDLKICKINGBYLENGTH.Plo
include ./$(DEPDIR)/ta_CDLLADDERBOTTOM.Plo
include ./$(DEPDIR)/ta_CDLLONGLEGGEDDOJI.Plo
include ./$(DEPDIR)/ta_CDLLONGLINE.Plo
include ./$(DEPDIR)/ta_CDLMARUBOZU.Plo
include ./$(DEPDIR)/ta_CDLMATCHINGLOW.Plo
include ./$(DEPDIR)/ta_CDLMATHOLD.Plo
include ./$(DEPDIR)/ta_CDLMORNINGDOJISTAR.Plo
include ./$(DEPDIR)/ta_CDLMORNINGSTAR.Plo
include ./$(DEPDIR)/ta_CDLONNECK.Plo
include ./$(DEPDIR)/ta_CDLPIERCING.Plo
include ./$(DEPDIR)/ta_CDLRICKSHAWMAN.Plo
include ./$(DEPDIR)/ta_CDLRISEFALL3METHODS.Plo
include ./$(DEPDIR)/ta_CDLSEPARATINGLINES.Plo
include ./$(DEPDIR)/ta_CDLSHOOTINGSTAR.Plo
include ./$(DEPDIR)/ta_CDLSHORTLINE.Plo
include ./$(DEPDIR)/ta_CDLSPINNINGTOP.Plo
include ./$(DEPDIR)/ta_CDLSTALLEDPATTERN.Plo
include ./$(DEPDIR)/ta_CDLSTICKSANDWICH.Plo
include ./$(DEPDIR)/ta_CDLTAKURI.Plo
include ./$(DEPDIR)/ta_CDLTASUKIGAP.Plo
include ./$(DEPDIR)/ta_CDLTHRUSTING.Plo
include ./$(DEPDIR)/ta_CDLTRISTAR.Plo
include ./$(DEPDIR)/ta_CDLUNIQUE3RIVER.Plo
include ./$(DEPDIR)/ta_CDLUPSIDEGAP2CROWS.Plo
include ./$(DEPDIR)/ta_CDLXSIDEGAP3METHODS.Plo
include ./$(DEPDIR)/ta_CEIL.Plo
include ./$(DEPDIR)/ta_CMO.Plo
include ./$(DEPDIR)/ta_CORREL.Plo
include ./$(DEPDIR)/ta_COS.Plo
include ./$(DEPDIR)/ta_COSH.Plo
include ./$(DEPDIR)/ta_DEMA.Plo
include ./$(DEPDIR)/ta_DIV.Plo
include ./$(DEPDIR)/ta_DX.Plo
include ./$(DEPDIR)/ta_EMA.Plo
include ./$(DEPDIR)/ta_EXP.Plo
include ./$(DEPDIR)/ta_FLOOR.Plo
include ./$(DEPDIR)/ta_HT_DCPERIOD.Plo
include ./$(DEPDIR)/ta_HT_DCPHASE.Plo
include ./$(DEPDIR)/ta_HT_PHASOR.Plo
include ./$(DEPDIR)/ta_HT_SINE.Plo
include ./$(DEPDIR)/ta_HT_TRENDLINE.Plo
include ./$(DEPDIR)/ta_HT_TRENDMODE.Plo
include ./$(DEPDIR)/ta_KAMA.Plo
include ./$(DEPDIR)/ta_LINEARREG.Plo
include ./$(DEPDIR)/ta_LINEARREG_ANGLE.Plo
include ./$(DEPDIR)/ta_LINEARREG_INTERCEPT.Plo
include ./$(DEPDIR)/ta_LINEARREG_SLOPE.Plo
include ./$(DEPDIR)/ta_LN.Plo
include ./$(DEPDIR)/ta_LOG10.Plo
include ./$(DEPDIR)/ta_MA.Plo
include ./$(DEPDIR)/ta_MACD.Plo
include ./$(DEPDIR)/ta_MACDEXT.Plo
include ./$(DEPDIR)/ta_MACDFIX.Plo
include ./$(DEPDIR)/ta_MAMA.Plo
include ./$(DEPDIR)/ta_MAVP.Plo
include ./$(DEPDIR)/ta_MAX.Plo
include ./$(DEPDIR)/ta_MAXINDEX.Plo
include ./$(DEPDIR)/ta_MEDPRICE.Plo
include ./$(DEPDIR)/ta_MFI.Plo
include ./$(DEPDIR)/ta_MIDPOINT.Plo
include ./$(DEPDIR)/ta_MIDPRICE.Plo
include ./$(DEPDIR)/ta_MIN.Plo
include ./$(DEPDIR)/ta_MININDEX.Plo
include ./$(DEPDIR)/ta_MINMAX.Plo
include ./$(DEPDIR)/ta_MINMAXINDEX.Plo
include ./$(DEPDIR)/ta_MINUS_DI.Plo
include ./$(DEPDIR)/ta_MINUS_DM.Plo
include ./$(DEPDIR)/ta_MOM.Plo
include ./$(DEPDIR)/ta_MULT.Plo
include ./$(DEPDIR)/ta_NATR.Plo
include ./$(DEPDIR)/ta_OBV.Plo
include ./$(DEPDIR)/ta_PLUS_DI.Plo
include ./$(DEPDIR)/ta_PLUS_DM.Plo
include ./$(DEPDIR)/ta_PPO.Plo
include ./$(DEPDIR)/ta_ROC.Plo
include ./$(DEPDIR)/ta_ROCP.Plo
include ./$(DEPDIR)/ta_ROCR.Plo
include ./$(DEPDIR)/ta_ROCR100.Plo
include ./$(DEPDIR)/ta_RSI.Plo
include ./$(DEPDIR)/ta_SAR.Plo
include ./$(DEPDIR)/ta_SAREXT.Plo
include ./$(DEPDIR)/ta_SIN.Plo
include ./$(DEPDIR)/ta_SINH.Plo
include ./$(DEPDIR)/ta_SMA.Plo
include ./$(DEPDIR)/ta_SQRT.Plo
include ./$(DEPDIR)/ta_STDDEV.Plo
include ./$(DEPDIR)/ta_STOCH.Plo
include ./$(DEPDIR)/ta_STOCHF.Plo
include ./$(DEPDIR)/ta_STOCHRSI.Plo
include ./$(DEPDIR)/ta_SUB.Plo
include ./$(DEPDIR)/ta_SUM.Plo
include ./$(DEPDIR)/ta_T3.Plo
include ./$(DEPDIR)/ta_TAN.Plo
include ./$(DEPDIR)/ta_TANH.Plo
include ./$(DEPDIR)/ta_TEMA.Plo
include ./$(DEPDIR)/ta_TRANGE.Plo
include ./$(DEPDIR)/ta_TRIMA.Plo
include ./$(DEPDIR)/ta_TRIX.Plo
include ./$(DEPDIR)/ta_TSF.Plo
include ./$(DEPDIR)/ta_TYPPRICE.Plo
include ./$(DEPDIR)/ta_ULTOSC.Plo
include ./$(DEPDIR)/ta_VAR.Plo
include ./$(DEPDIR)/ta_WCLPRICE.Plo
include ./$(DEPDIR)/ta_WILLR.Plo
include ./$(DEPDIR)/ta_WMA.Plo
include ./$(DEPDIR)/ta_utility.Plo

.c.o:
	$(COMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ $<
	mv -f $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Po
#	source='$<' object='$@' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(COMPILE) -c $<

.c.obj:
	$(COMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ `$(CYGPATH_W) '$<'`
	mv -f $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Po
#	source='$<' object='$@' libtool=no \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(COMPILE) -c `$(CYGPATH_W) '$<'`

.c.lo:
	$(LTCOMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ $<
	mv -f $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Plo
#	source='$<' object='$@' libtool=yes \
#	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) \
#	$(LTCOMPILE) -c -o $@ $<

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs
install-libta_funcHEADERS: $(libta_func_HEADERS)
	@$(NORMAL_INSTALL)
	test -z "$(libta_funcdir)" || $(MKDIR_P) "$(DESTDIR)$(libta_funcdir)"
	@list='$(libta_func_HEADERS)'; for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  f=$(am__strip_dir) \
	  echo " $(libta_funcHEADERS_INSTALL) '$$d$$p' '$(DESTDIR)$(libta_funcdir)/$$f'"; \
	  $(libta_funcHEADERS_INSTALL) "$$d$$p" "$(DESTDIR)$(libta_funcdir)/$$f"; \
	done

uninstall-libta_funcHEADERS:
	@$(NORMAL_UNINSTALL)
	@list='$(libta_func_HEADERS)'; for p in $$list; do \
	  f=$(am__strip_dir) \
	  echo " rm -f '$(DESTDIR)$(libta_funcdir)/$$f'"; \
	  rm -f "$(DESTDIR)$(libta_funcdir)/$$f"; \
	done

ID: $(HEADERS) $(SOURCES) $(LISP) $(TAGS_FILES)
	list='$(SOURCES) $(HEADERS) $(LISP) $(TAGS_FILES)'; \
	unique=`for i in $$list; do \
	    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
	  done | \
	  $(AWK) '    { files[$$0] = 1; } \
	       END { for (i in files) print i; }'`; \
	mkid -fID $$unique
tags: TAGS

TAGS:  $(HEADERS) $(SOURCES)  $(TAGS_DEPENDENCIES) \
		$(TAGS_FILES) $(LISP)
	tags=; \
	here=`pwd`; \
	list='$(SOURCES) $(HEADERS)  $(LISP) $(TAGS_FILES)'; \
	unique=`for i in $$list; do \
	    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
	  done | \
	  $(AWK) '    { files[$$0] = 1; } \
	       END { for (i in files) print i; }'`; \
	if test -z "$(ETAGS_ARGS)$$tags$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	    $$tags $$unique; \
	fi
ctags: CTAGS
CTAGS:  $(HEADERS) $(SOURCES)  $(TAGS_DEPENDENCIES) \
		$(TAGS_FILES) $(LISP)
	tags=; \
	here=`pwd`; \
	list='$(SOURCES) $(HEADERS)  $(LISP) $(TAGS_FILES)'; \
	unique=`for i in $$list; do \
	    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
	  done | \
	  $(AWK) '    { files[$$0] = 1; } \
	       END { for (i in files) print i; }'`; \
	test -z "$(CTAGS_ARGS)$$tags$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$tags $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && cd $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) $$here

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags

distdir: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -pR $(srcdir)/$$file $(distdir)$$dir || exit 1; \
	    fi; \
	    cp -pR $$d/$$file $(distdir)$$dir || exit 1; \
	  else \
	    test -f $(distdir)/$$file \
	    || cp -p $$d/$$file $(distdir)/$$file \
	    || exit 1; \
	  fi; \
	done
check-am: all-am
check: check-am
all-am: Makefile $(LTLIBRARIES) $(HEADERS)
installdirs:
	for dir in "$(DESTDIR)$(libta_funcdir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	$(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	  install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	  `test -z '$(STRIP)' || \
	    echo "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'"` install
mostlyclean-generic:

clean-generic:

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
clean: clean-am

clean-am: clean-generic clean-libtool clean-noinstLTLIBRARIES \
	mostlyclean-am

distclean: distclean-am
	-rm -rf ./$(DEPDIR)
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-tags

dvi: dvi-am

dvi-am:

html: html-am

info: info-am

info-am:

install-data-am: install-libta_funcHEADERS

install-dvi: install-dvi-am

install-exec-am:

install-html: install-html-am

install-info: install-info-am

install-man:

install-pdf: install-pdf-am

install-ps: install-ps-am

installcheck-am:

maintainer-clean: maintainer-clean-am
	-rm -rf ./$(DEPDIR)
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am: uninstall-libta_funcHEADERS

.MAKE: install-am install-strip

.PHONY: CTAGS GTAGS all all-am check check-am clean clean-generic \
	clean-libtool clean-noinstLTLIBRARIES ctags distclean \
	distclean-compile distclean-generic distclean-libtool \
	distclean-tags distdir dvi dvi-am html html-am info info-am \
	install install-am install-data install-data-am install-dvi \
	install-dvi-am install-exec install-exec-am install-html \
	install-html-am install-info install-info-am \
	install-libta_funcHEADERS install-man install-pdf \
	install-pdf-am install-ps install-ps-am install-strip \
	installcheck installcheck-am installdirs maintainer-clean \
	maintainer-clean-generic mostlyclean mostlyclean-compile \
	mostlyclean-generic mostlyclean-libtool pdf pdf-am ps ps-am \
	tags uninstall uninstall-am uninstall-libta_funcHEADERS

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:
