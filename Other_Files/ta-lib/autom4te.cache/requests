# This file was generated by Autom4te Wed Dec 20 15:07:46 UTC 2006.
# It contains the lists of macros which have been traced.
# It can be safely removed.

@request = (
             bless( [
                      '0',
                      1,
                      [
                        '/usr/share/autoconf'
                      ],
                      [
                        '/usr/share/autoconf/autoconf/autoconf.m4f',
                        '/usr/share/aclocal/libtool.m4',
                        '/usr/share/aclocal-1.10/amversion.m4',
                        '/usr/share/aclocal-1.10/auxdir.m4',
                        '/usr/share/aclocal-1.10/cond.m4',
                        '/usr/share/aclocal-1.10/depend.m4',
                        '/usr/share/aclocal-1.10/depout.m4',
                        '/usr/share/aclocal-1.10/init.m4',
                        '/usr/share/aclocal-1.10/install-sh.m4',
                        '/usr/share/aclocal-1.10/lead-dot.m4',
                        '/usr/share/aclocal-1.10/make.m4',
                        '/usr/share/aclocal-1.10/missing.m4',
                        '/usr/share/aclocal-1.10/mkdirp.m4',
                        '/usr/share/aclocal-1.10/options.m4',
                        '/usr/share/aclocal-1.10/runlog.m4',
                        '/usr/share/aclocal-1.10/sanity.m4',
                        '/usr/share/aclocal-1.10/strip.m4',
                        '/usr/share/aclocal-1.10/substnot.m4',
                        '/usr/share/aclocal-1.10/tar.m4',
                        'configure.in'
                      ],
                      {
                        '_LT_AC_TAGCONFIG' => 1,
                        'AM_ENABLE_STATIC' => 1,
                        'm4_pattern_forbid' => 1,
                        'AC_LIBTOOL_COMPILER_OPTION' => 1,
                        'AC_LIBTOOL_LANG_RC_CONFIG' => 1,
                        '_LT_AC_SHELL_INIT' => 1,
                        'AC_DISABLE_SHARED' => 1,
                        'AC_DEFUN' => 1,
                        '_LT_COMPILER_BOILERPLATE' => 1,
                        'AC_PROG_LIBTOOL' => 1,
                        'AC_LIBTOOL_SETUP' => 1,
                        'AC_LIBTOOL_WIN32_DLL' => 1,
                        '_LT_AC_LANG_CXX_CONFIG' => 1,
                        'AM_PROG_MKDIR_P' => 1,
                        'AC_PROG_LD_RELOAD_FLAG' => 1,
                        'AM_AUTOMAKE_VERSION' => 1,
                        'AM_MISSING_HAS_RUN' => 1,
                        'AM_MISSING_PROG' => 1,
                        'AC_LIBTOOL_DLOPEN_SELF' => 1,
                        'AC_LIBTOOL_PROG_LD_HARDCODE_LIBPATH' => 1,
                        '_LT_AC_LANG_C_CONFIG' => 1,
                        'AC_LIBTOOL_PROG_LD_SHLIBS' => 1,
                        'AM_PROG_INSTALL_STRIP' => 1,
                        '_m4_warn' => 1,
                        'AC_LIBTOOL_OBJDIR' => 1,
                        'AM_SANITY_CHECK' => 1,
                        'AC_LIBTOOL_LINKER_OPTION' => 1,
                        'AC_LIBTOOL_PROG_COMPILER_PIC' => 1,
                        'AC_LIBTOOL_LANG_GCJ_CONFIG' => 1,
                        '_LT_AC_CHECK_DLFCN' => 1,
                        'AC_LIBTOOL_SYS_GLOBAL_SYMBOL_PIPE' => 1,
                        'AC_LIBTOOL_CXX' => 1,
                        'LT_AC_PROG_RC' => 1,
                        '_AM_PROG_TAR' => 1,
                        'LT_AC_PROG_GCJ' => 1,
                        'AC_LIBTOOL_GCJ' => 1,
                        'AM_DEP_TRACK' => 1,
                        '_LT_AC_PROG_CXXCPP' => 1,
                        'AM_DISABLE_STATIC' => 1,
                        '_AC_PROG_LIBTOOL' => 1,
                        '_LT_AC_LANG_F77' => 1,
                        'AC_LIBTOOL_CONFIG' => 1,
                        '_AM_IF_OPTION' => 1,
                        '_AM_SUBST_NOTMAKE' => 1,
                        'AC_PATH_TOOL_PREFIX' => 1,
                        'AC_LIBTOOL_F77' => 1,
                        '_AM_AUTOCONF_VERSION' => 1,
                        'm4_pattern_allow' => 1,
                        'AM_DISABLE_SHARED' => 1,
                        'AM_SET_LEADING_DOT' => 1,
                        '_LT_AC_LANG_CXX' => 1,
                        'AM_PROG_LIBTOOL' => 1,
                        '_AM_DEPENDENCIES' => 1,
                        '_LT_AC_FILE_LTDLL_C' => 1,
                        'AM_PROG_LD' => 1,
                        'AC_LIBTOOL_LANG_C_CONFIG' => 1,
                        '_LT_AC_SYS_COMPILER' => 1,
                        'AM_PROG_NM' => 1,
                        'AU_DEFUN' => 1,
                        'AC_PROG_NM' => 1,
                        'AC_LIBTOOL_DLOPEN' => 1,
                        'AC_LIBLTDL_CONVENIENCE' => 1,
                        'AC_PROG_LD' => 1,
                        'AC_PROG_LD_GNU' => 1,
                        'AC_ENABLE_FAST_INSTALL' => 1,
                        'AC_DEPLIBS_CHECK_METHOD' => 1,
                        'AC_LIBLTDL_INSTALLABLE' => 1,
                        'AM_SET_CURRENT_AUTOMAKE_VERSION' => 1,
                        'AC_LIBTOOL_SYS_DYNAMIC_LINKER' => 1,
                        '_AM_SET_OPTION' => 1,
                        '_LT_LINKER_BOILERPLATE' => 1,
                        'AC_LIBTOOL_PROG_CC_C_O' => 1,
                        'AC_LIBTOOL_LANG_CXX_CONFIG' => 1,
                        'AM_INIT_AUTOMAKE' => 1,
                        'AC_DISABLE_STATIC' => 1,
                        'AM_OUTPUT_DEPENDENCY_COMMANDS' => 1,
                        'AC_DEFUN_ONCE' => 1,
                        '_LT_AC_LOCK' => 1,
                        '_LT_AC_LANG_RC_CONFIG' => 1,
                        '_LT_AC_LANG_GCJ' => 1,
                        'AC_LIBTOOL_RC' => 1,
                        '_LT_AC_PROG_ECHO_BACKSLASH' => 1,
                        'AC_DISABLE_FAST_INSTALL' => 1,
                        'AC_LIBTOOL_POSTDEP_PREDEP' => 1,
                        '_LT_AC_TRY_DLOPEN_SELF' => 1,
                        '_LT_AC_SYS_LIBPATH_AIX' => 1,
                        'include' => 1,
                        'LT_AC_PROG_SED' => 1,
                        'AM_ENABLE_SHARED' => 1,
                        'AM_AUX_DIR_EXPAND' => 1,
                        '_LT_AC_LANG_GCJ_CONFIG' => 1,
                        'AC_ENABLE_SHARED' => 1,
                        'AC_LIBTOOL_PROG_COMPILER_NO_RTTI' => 1,
                        '_LT_AC_LANG_F77_CONFIG' => 1,
                        '_AM_SET_OPTIONS' => 1,
                        'AM_RUN_LOG' => 1,
                        '_AM_OUTPUT_DEPENDENCY_COMMANDS' => 1,
                        'AC_LIBTOOL_PICMODE' => 1,
                        'AC_ENABLE_STATIC' => 1,
                        'AC_LIBTOOL_SYS_HARD_LINK_LOCKS' => 1,
                        'AC_CHECK_LIBM' => 1,
                        '_LT_AC_TAGVAR' => 1,
                        'AC_LIBTOOL_SYS_LIB_STRIP' => 1,
                        '_AM_MANGLE_OPTION' => 1,
                        'AC_LIBTOOL_LANG_F77_CONFIG' => 1,
                        'AM_CONDITIONAL' => 1,
                        'AC_LIBTOOL_SYS_MAX_CMD_LEN' => 1,
                        'AM_SET_DEPDIR' => 1,
                        '_LT_CC_BASENAME' => 1,
                        'AM_PROG_INSTALL_SH' => 1,
                        'm4_include' => 1,
                        'AC_PROG_EGREP' => 1,
                        'AC_PATH_MAGIC' => 1,
                        '_AC_AM_CONFIG_HEADER_HOOK' => 1,
                        'AM_MAKE_INCLUDE' => 1
                      }
                    ], 'Autom4te::Request' ),
             bless( [
                      '1',
                      1,
                      [
                        '/usr/share/autoconf'
                      ],
                      [
                        '/usr/share/autoconf/autoconf/autoconf.m4f',
                        'aclocal.m4',
                        'configure.in'
                      ],
                      {
                        '_LT_AC_TAGCONFIG' => 1,
                        'AM_PROG_F77_C_O' => 1,
                        'AC_INIT' => 1,
                        'm4_pattern_forbid' => 1,
                        'AC_CANONICAL_TARGET' => 1,
                        'AC_SUBST' => 1,
                        'AC_CONFIG_LIBOBJ_DIR' => 1,
                        'AC_FC_SRCEXT' => 1,
                        'AC_CANONICAL_HOST' => 1,
                        'AC_PROG_LIBTOOL' => 1,
                        'AM_INIT_AUTOMAKE' => 1,
                        'AC_CONFIG_SUBDIRS' => 1,
                        'AM_AUTOMAKE_VERSION' => 1,
                        'LT_CONFIG_LTDL_DIR' => 1,
                        'AC_REQUIRE_AUX_FILE' => 1,
                        'AC_CONFIG_LINKS' => 1,
                        'm4_sinclude' => 1,
                        'LT_SUPPORTED_TAG' => 1,
                        'AM_MAINTAINER_MODE' => 1,
                        'AM_GNU_GETTEXT_INTL_SUBDIR' => 1,
                        '_m4_warn' => 1,
                        'AM_PROG_CXX_C_O' => 1,
                        'AM_ENABLE_MULTILIB' => 1,
                        'AC_CONFIG_FILES' => 1,
                        'include' => 1,
                        'LT_INIT' => 1,
                        'AM_GNU_GETTEXT' => 1,
                        'AC_LIBSOURCE' => 1,
                        'AM_PROG_FC_C_O' => 1,
                        'AC_CANONICAL_BUILD' => 1,
                        'AC_FC_FREEFORM' => 1,
                        'AH_OUTPUT' => 1,
                        '_AM_SUBST_NOTMAKE' => 1,
                        'AC_CONFIG_AUX_DIR' => 1,
                        'sinclude' => 1,
                        'AM_PROG_CC_C_O' => 1,
                        'm4_pattern_allow' => 1,
                        'AC_CANONICAL_SYSTEM' => 1,
                        'AM_CONDITIONAL' => 1,
                        'AC_CONFIG_HEADERS' => 1,
                        'AC_DEFINE_TRACE_LITERAL' => 1,
                        'm4_include' => 1,
                        'AC_SUBST_TRACE' => 1
                      }
                    ], 'Autom4te::Request' )
           );

