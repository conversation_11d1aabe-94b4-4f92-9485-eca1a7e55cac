# 🎯 Multi-Timeframe Live Data Fetcher with WMA Crossover Detection - Setup Guide

## 📋 **Prerequisites**

### **Python Version**
- **Python 3.8 or higher** (recommended: Python 3.9+)
- Check your Python version: `python --version` or `python3 --version`

### **Package Manager**
- **uv** (recommended) or **pip**
- Install uv: `curl -LsSf https://astral.sh/uv/install.sh | sh`

### **DhanHQ Account**
- Active DhanHQ trading account
- API access enabled
- Valid Client ID and Access Token

## 🔧 **Installation Steps**

### **Step 1: Clone or Download the Project**
```bash
# If using git
git clone <repository-url>
cd multi-timeframe-live-fetcher

# Or download and extract the ZIP file
```

### **Step 2: Install Dependencies**

#### **Option A: Using uv (Recommended)**
```bash
# Install uv if not already installed
curl -LsSf https://astral.sh/uv/install.sh | sh

# Install minimal dependencies
uv pip install -r requirements-minimal.txt

# Or install full dependencies
uv pip install -r requirements.txt
```

#### **Option B: Using pip with Virtual Environment**
```bash
# Create virtual environment
python -m venv .venv

# Activate virtual environment
# On Windows:
.venv\Scripts\activate

# On macOS/Linux:
source .venv/bin/activate

# Install dependencies
pip install -r requirements-minimal.txt
```

#### **Option C: Direct Installation**
```bash
# Essential packages only
pip install dhanhq>=2.2.0 pandas>=2.0.0 numpy>=1.24.0

# Or install latest versions
pip install dhanhq pandas numpy
```

### **Step 3: Verify Installation**
```bash
python -c "import dhanhq, pandas, numpy; print('✅ All dependencies installed successfully')"
```

## ⚙️ **Configuration**

### **Step 1: Update API Credentials**
Edit the `multi_timeframe_live_fetcher.py` file and update:

```python
# API Configuration
CLIENT_ID = "YOUR_CLIENT_ID"
ACCESS_TOKEN = "YOUR_ACCESS_TOKEN"
```

### **Step 2: Verify DhanHQ CSV Files**
Ensure these files are in the project directory:
- `dhan_nse_eq.csv` (NSE Equity securities)
- `dhan_nse_idx.csv` (NSE Index securities)

Download from: [DhanHQ GitHub Repository](https://github.com/dhan-oss/DhanHQ-py)

## 🏃‍♂️ **Running the Script**

### **Basic Execution**
```bash
# Using uv (recommended)
uv run python multi_timeframe_live_fetcher.py

# Or using standard python (if virtual environment is activated)
python multi_timeframe_live_fetcher.py
```

### **Generate Crossover Indicators for Existing Data**
```bash
# Process existing historical data
uv run python generate_crossover_indicators.py
```

### **Background Execution (Linux/macOS)**
```bash
# Run in background
nohup uv run python multi_timeframe_live_fetcher.py > output.log 2>&1 &

# Check if running
ps aux | grep multi_timeframe_live_fetcher.py
```

### **Background Execution (Windows)**
```bash
# Run in background (PowerShell)
Start-Process python -ArgumentList "multi_timeframe_live_fetcher.py" -WindowStyle Hidden
```

## 📁 **Project Structure**

After running, your project will have:

```
multi-timeframe-live-fetcher/
├── multi_timeframe_live_fetcher.py    # Main crossover detection script
├── generate_crossover_indicators.py   # Utility script for existing data
├── requirements.txt                   # Full dependencies
├── requirements-minimal.txt           # Essential dependencies only
├── README.md                          # Main documentation
├── SETUP_GUIDE.md                    # This setup guide
├── dhan_nse_eq.csv                   # NSE equity securities
├── dhan_nse_idx.csv                  # NSE index securities
├── Historical_Data_Layer/            # Historical data with rolling windows
│   ├── historical_1min.csv          # 3 days rolling (with wma5_10 column)
│   ├── historical_5min.csv          # 7 days rolling (with wma5_10 column)
│   └── historical_60min.csv         # 30 days rolling (with wma5_10 column)
├── Live_Data_Layer/                  # Live data collection
│   ├── NIFTY_INDEX_1min_live.csv
│   ├── NIFTY_INDEX_5min_live.csv
│   └── NIFTY_INDEX_60min_live.csv
└── .venv/                            # Virtual environment (if created)
```

## 🔍 **Verification**

### **Check Script Status**
```bash
# View recent logs
tail -f multi_timeframe_fetcher.log

# Check generated data files
ls -la Historical_Data_Layer/
ls -la Live_Data_Layer/

# View sample data with crossover indicators
head Historical_Data_Layer/historical_5min.csv
```

### **Expected Output**
When running successfully, you should see:
```
🚀 Multi-Timeframe Live Data Fetcher with Crossover Detection Started
⏰ Current time: 2025-06-03 08:30:00
📅 Today is a trading day
⏳ Market opens at 09:15:00. Waiting...
[1min] 🚨 NEW LIVE CROSSOVER: UP at 2025-06-03 10:15:00
[1min] 📊 WMA5: 24755.23, WMA10: 24752.45
```

## 🛠️ **Troubleshooting**

### **Common Issues**

#### **1. Import Error: No module named 'dhanhq'**
```bash
# Solution: Install dhanhq
pip install dhanhq
```

#### **2. Import Error: No module named 'pandas'**
```bash
# Solution: Install pandas
pip install pandas
```

#### **3. API Authentication Error**
- Verify your CLIENT_ID and ACCESS_TOKEN
- Check if your DhanHQ API access is active
- Ensure tokens haven't expired

#### **4. No Data Generated**
- Check if market is open (9:15 AM - 3:30 PM IST on trading days)
- Verify internet connection
- Check logs/activity_log.csv for error messages

#### **5. Permission Denied (Linux/macOS)**
```bash
# Make script executable
chmod +x DhanHQ_Enhanced_Live_Fetcher.py
```

### **Debug Mode**
Add debug prints to the script for troubleshooting:
```python
print(f"Debug: Current time = {get_current_time()}")
print(f"Debug: Market open = {is_market_open()}")
print(f"Debug: Trading day = {is_trading_day(get_today())}")
```

## 📊 **Data Output**

### **Generated Files**
- **Intraday Data**: `stock_data/{STOCK}_{TIMEFRAME}.csv`
- **Live Quotes**: `stock_data/live_quotes.csv`
- **Activity Logs**: `logs/activity_log.csv`

### **Data Format**
```csv
open,high,low,close,volume,timestamp
1941.9,1944.2,1940.7,1942.7,128805.0,2025-06-03 09:16:00
```

## 🔄 **Updates and Maintenance**

### **Update Dependencies**
```bash
# Update to latest versions
pip install --upgrade dhanhq pandas

# Or reinstall from requirements
pip install -r requirements-minimal.txt --upgrade
```

### **Update Security IDs**
If you need to add more stocks:
1. Look up security IDs in `dhan_nse_eq.csv` or `dhan_nse_idx.csv`
2. Add to the `STOCKS` list in the script
3. Restart the script

## 🎯 **Production Deployment**

### **Systemd Service (Linux)**
Create `/etc/systemd/system/dhan-fetcher.service`:
```ini
[Unit]
Description=DhanHQ Live Data Fetcher
After=network.target

[Service]
Type=simple
User=your-username
WorkingDirectory=/path/to/dhan-live-fetcher
ExecStart=/path/to/.venv/bin/python DhanHQ_Enhanced_Live_Fetcher.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

Enable and start:
```bash
sudo systemctl enable dhan-fetcher
sudo systemctl start dhan-fetcher
sudo systemctl status dhan-fetcher
```

### **Docker Deployment**
Create `Dockerfile`:
```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements-minimal.txt .
RUN pip install -r requirements-minimal.txt
COPY . .
CMD ["python", "DhanHQ_Enhanced_Live_Fetcher.py"]
```

Build and run:
```bash
docker build -t dhan-fetcher .
docker run -d --name dhan-fetcher -v $(pwd)/stock_data:/app/stock_data dhan-fetcher
```

## 📞 **Support**

### **Resources**
- **DhanHQ API Documentation**: https://dhanhq.co/docs/
- **DhanHQ Python Library**: https://github.com/dhan-oss/DhanHQ-py
- **Pandas Documentation**: https://pandas.pydata.org/docs/

### **Common Commands**
```bash
# Check Python version
python --version

# List installed packages
pip list

# Check virtual environment
which python

# View real-time logs
tail -f logs/activity_log.csv

# Stop background process
pkill -f DhanHQ_Enhanced_Live_Fetcher.py
```

## ✅ **Success Checklist**

- [ ] Python 3.8+ installed
- [ ] Virtual environment created and activated
- [ ] Dependencies installed successfully
- [ ] DhanHQ credentials configured
- [ ] CSV files present (dhan_nse_eq.csv, dhan_nse_idx.csv)
- [ ] Script runs without errors
- [ ] Data files generated in stock_data/
- [ ] Logs created in logs/activity_log.csv
- [ ] Live data collection working during market hours

**🎉 You're all set! The DhanHQ Enhanced Live Data Fetcher is ready for production use.**
