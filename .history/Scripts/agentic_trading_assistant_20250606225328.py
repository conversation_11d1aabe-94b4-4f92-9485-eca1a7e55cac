#!/usr/bin/env python3
"""
🧠 AGENTIC INDIAN MARKET LIVE TRADING ASSISTANT
Advanced WMA Crossover & Zone Detection Command Interface

This module provides a comprehensive command-line interface for the Indian Market
Live Trading Assistant with WMA crossover and supply/demand zone detection.

🎯 CORE CAPABILITIES:
- Initialize WMA buffers with historical data
- Real-time crossover signal detection
- Supply/demand zone analysis
- Zone continuation tracking
- Performance validation and testing

🔥 USAGE EXAMPLES:
python agentic_trading_assistant.py --initialize-buffers --timeframes 1,5,60
python agentic_trading_assistant.py --show-crossovers --timeframe 5 --today
python agentic_trading_assistant.py --list-zones --timeframe 1 --zone-type demand
python agentic_trading_assistant.py --export-wma --timeframe 60 --output wma_data.csv
"""

import os
import sys
import argparse
import datetime
import pandas as pd
import logging
from typing import List, Dict, Optional

# Add the Scripts directory to Python path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the main trading system
from multi_timeframe_live_fetcher import (
    TIMEFRAMES, IST, get_current_time_ist, is_market_open,
    initialize_csv_files, historical_data_storage, active_zones_tracker,
    live_signals_tracker, validate_wma_readiness, calculate_wma_signals,
    fetch_comprehensive_historical_data, prepare_zones_for_tomorrow,
    get_output_filename
)

# Configure logging for the assistant
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - [AGENTIC] - %(message)s',
    handlers=[
        logging.FileHandler('agentic_assistant.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AgenticTradingAssistant:
    """
    🧠 AGENTIC TRADING ASSISTANT CLASS
    
    Provides intelligent command-line interface for WMA crossover trading system
    """
    
    def __init__(self):
        self.supported_timeframes = ['1', '5', '60']
        logger.info("🚀 Agentic Trading Assistant initialized")
    
    def initialize_wma_buffers(self, timeframes: List[str]) -> Dict:
        """
        🔄 INITIALIZE WMA BUFFERS FOR SPECIFIED TIMEFRAMES
        
        Loads historical data and prepares WMA calculation buffers
        """
        logger.info(f"🔄 Initializing WMA buffers for timeframes: {timeframes}")
        results = {}
        
        for tf in timeframes:
            if tf not in self.supported_timeframes:
                logger.warning(f"⚠️ Unsupported timeframe: {tf}")
                continue
                
            tf_name = TIMEFRAMES[tf]['name']
            logger.info(f"[{tf_name}] Starting buffer initialization...")
            
            try:
                # Initialize CSV files and load historical data
                has_historical, has_live, wma_readiness = initialize_csv_files(tf)
                
                # Fetch additional data if needed
                if not wma_readiness['is_ready']:
                    logger.info(f"[{tf_name}] Fetching additional historical data...")
                    fetch_comprehensive_historical_data(tf)
                    wma_readiness = validate_wma_readiness(tf)
                
                results[tf] = {
                    'timeframe': tf_name,
                    'has_historical': has_historical,
                    'has_live': has_live,
                    'wma_ready': wma_readiness['is_ready'],
                    'candle_count': wma_readiness['current_candles'],
                    'required_candles': wma_readiness['required_candles'],
                    'readiness_percentage': wma_readiness['readiness_percentage']
                }
                
                logger.info(f"[{tf_name}] ✅ Buffer initialization complete - "
                           f"Ready: {wma_readiness['is_ready']}, "
                           f"Candles: {wma_readiness['current_candles']}")
                
            except Exception as e:
                logger.error(f"[{tf_name}] ❌ Buffer initialization failed: {str(e)}")
                results[tf] = {'error': str(e)}
        
        return results
    
    def get_first_valid_crossover(self, timeframe: str, today_only: bool = True) -> Optional[Dict]:
        """
        🎯 GET FIRST VALID WMA CROSSOVER SIGNAL
        
        Returns the first valid WMA5/WMA10 crossover signal with exact timing
        """
        if timeframe not in self.supported_timeframes:
            logger.error(f"❌ Unsupported timeframe: {timeframe}")
            return None
            
        tf_name = TIMEFRAMES[timeframe]['name']
        logger.info(f"[{tf_name}] Searching for first valid crossover signal...")
        
        try:
            # Get current data from deque
            current_data = list(historical_data_storage[timeframe])
            
            if len(current_data) < 10:
                logger.warning(f"[{tf_name}] Insufficient data for crossover analysis")
                return None
            
            # Calculate WMA signals
            analysis_result = calculate_wma_signals(current_data)
            
            if not analysis_result or not analysis_result['signals']:
                logger.info(f"[{tf_name}] No crossover signals found")
                return None
            
            # Filter for today's signals if requested
            if today_only:
                today = get_current_time_ist().date()
                today_signals = []
                
                for signal in analysis_result['signals']:
                    signal_time = pd.to_datetime(signal['timestamp'])
                    if signal_time.date() == today:
                        today_signals.append(signal)
                
                if not today_signals:
                    logger.info(f"[{tf_name}] No crossover signals found for today")
                    return None
                
                first_signal = today_signals[0]
            else:
                first_signal = analysis_result['signals'][0]
            
            # Format the result
            result = {
                'timeframe': tf_name,
                'timestamp': first_signal['timestamp'],
                'signal_type': first_signal['signal'],
                'price_level': first_signal['price_level'],
                'wma5': round(first_signal['wma5'], 2),
                'wma10': round(first_signal['wma10'], 2),
                'candle_data': {
                    'open': first_signal['candle_data']['open'],
                    'high': first_signal['candle_data']['high'],
                    'low': first_signal['candle_data']['low'],
                    'close': first_signal['candle_data']['close'],
                    'volume': first_signal['candle_data']['volume']
                }
            }
            
            logger.info(f"[{tf_name}] ✅ First crossover: {result['signal_type']} at "
                       f"{result['timestamp']} (Price: {result['price_level']:.2f})")
            
            return result
            
        except Exception as e:
            logger.error(f"[{tf_name}] ❌ Error finding first crossover: {str(e)}")
            return None
    
    def list_demand_zones_after_down_crossover(self, timeframe: str) -> List[Dict]:
        """
        🏪 LIST ALL DEMAND ZONES FORMED AFTER DOWN CROSSOVERS
        
        Returns comprehensive list of demand zones with metadata
        """
        if timeframe not in self.supported_timeframes:
            logger.error(f"❌ Unsupported timeframe: {timeframe}")
            return []
            
        tf_name = TIMEFRAMES[timeframe]['name']
        logger.info(f"[{tf_name}] Searching for demand zones after DOWN crossovers...")
        
        try:
            # Check zones CSV file
            zones_folder = "Supply_Demand_Zones"
            zones_file = os.path.join(zones_folder, f'zones_{tf_name}.csv')
            
            if not os.path.exists(zones_file):
                logger.info(f"[{tf_name}] No zones file found")
                return []
            
            zones_df = pd.read_csv(zones_file)
            
            # Filter for demand zones that started with DOWN crossover
            demand_zones_after_down = zones_df[
                (zones_df['zone_type'] == 'DEMAND') &
                (zones_df['start_signal'] == 'DOWN')
            ]
            
            if demand_zones_after_down.empty:
                logger.info(f"[{tf_name}] No demand zones found after DOWN crossovers")
                return []
            
            # Format results
            results = []
            for _, zone in demand_zones_after_down.iterrows():
                result = {
                    'timeframe': tf_name,
                    'zone_type': zone['zone_type'],
                    'zone_level': zone['zone_level'],
                    'zone_strength': zone['zone_strength'],
                    'start_timestamp': zone['start_timestamp'],
                    'end_timestamp': zone['end_timestamp'],
                    'start_signal': zone['start_signal'],
                    'end_signal': zone['end_signal'],
                    'candle_count': zone['candle_count'],
                    'price_range': zone.get('price_range', 0)
                }
                results.append(result)
            
            logger.info(f"[{tf_name}] ✅ Found {len(results)} demand zones after DOWN crossovers")
            
            return results
            
        except Exception as e:
            logger.error(f"[{tf_name}] ❌ Error listing demand zones: {str(e)}")
            return []
    
    def export_wma_values_with_crossovers(self, timeframe: str, output_file: Optional[str] = None) -> str:
        """
        📊 EXPORT WMA VALUES WITH CROSSOVER MARKERS TO CSV
        
        Creates comprehensive CSV export with WMA data and crossover signals
        """
        if timeframe not in self.supported_timeframes:
            logger.error(f"❌ Unsupported timeframe: {timeframe}")
            return ""
            
        tf_name = TIMEFRAMES[timeframe]['name']
        
        if not output_file:
            timestamp = get_current_time_ist().strftime("%Y%m%d_%H%M%S")
            output_file = f"wma_export_{tf_name}_{timestamp}.csv"
        
        logger.info(f"[{tf_name}] Exporting WMA data to {output_file}...")
        
        try:
            # Get live data file
            live_file = get_output_filename(timeframe, 'live')
            
            if not os.path.exists(live_file):
                logger.error(f"[{tf_name}] Live data file not found")
                return ""
            
            # Read and process data
            df = pd.read_csv(live_file)
            
            if df.empty:
                logger.warning(f"[{tf_name}] No data to export")
                return ""
            
            # Select relevant columns for export
            export_columns = [
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'wma5', 'wma10', 'wma5_10'
            ]
            
            # Filter available columns
            available_columns = [col for col in export_columns if col in df.columns]
            export_df = df[available_columns].copy()
            
            # Add crossover markers
            if 'wma5_10' in export_df.columns:
                export_df['crossover_marker'] = export_df['wma5_10'].apply(
                    lambda x: '🔼' if x == 'UP' else '🔽' if x == 'DOWN' else '➡️'
                )
            
            # Save export file
            export_df.to_csv(output_file, index=False)
            
            logger.info(f"[{tf_name}] ✅ Exported {len(export_df)} rows to {output_file}")
            
            return output_file
            
        except Exception as e:
            logger.error(f"[{tf_name}] ❌ Error exporting WMA data: {str(e)}")
            return ""
