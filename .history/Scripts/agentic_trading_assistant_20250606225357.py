#!/usr/bin/env python3
"""
🧠 AGENTIC INDIAN MARKET LIVE TRADING ASSISTANT
Advanced WMA Crossover & Zone Detection Command Interface

This module provides a comprehensive command-line interface for the Indian Market
Live Trading Assistant with WMA crossover and supply/demand zone detection.

🎯 CORE CAPABILITIES:
- Initialize WMA buffers with historical data
- Real-time crossover signal detection
- Supply/demand zone analysis
- Zone continuation tracking
- Performance validation and testing

🔥 USAGE EXAMPLES:
python agentic_trading_assistant.py --initialize-buffers --timeframes 1,5,60
python agentic_trading_assistant.py --show-crossovers --timeframe 5 --today
python agentic_trading_assistant.py --list-zones --timeframe 1 --zone-type demand
python agentic_trading_assistant.py --export-wma --timeframe 60 --output wma_data.csv
"""

import os
import sys
import argparse
import datetime
import pandas as pd
import logging
from typing import List, Dict, Optional

# Add the Scripts directory to Python path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the main trading system
from multi_timeframe_live_fetcher import (
    TIMEFRAMES, IST, get_current_time_ist, is_market_open,
    initialize_csv_files, historical_data_storage, active_zones_tracker,
    live_signals_tracker, validate_wma_readiness, calculate_wma_signals,
    fetch_comprehensive_historical_data, prepare_zones_for_tomorrow,
    get_output_filename
)

# Configure logging for the assistant
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - [AGENTIC] - %(message)s',
    handlers=[
        logging.FileHandler('agentic_assistant.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AgenticTradingAssistant:
    """
    🧠 AGENTIC TRADING ASSISTANT CLASS
    
    Provides intelligent command-line interface for WMA crossover trading system
    """
    
    def __init__(self):
        self.supported_timeframes = ['1', '5', '60']
        logger.info("🚀 Agentic Trading Assistant initialized")
    
    def initialize_wma_buffers(self, timeframes: List[str]) -> Dict:
        """
        🔄 INITIALIZE WMA BUFFERS FOR SPECIFIED TIMEFRAMES
        
        Loads historical data and prepares WMA calculation buffers
        """
        logger.info(f"🔄 Initializing WMA buffers for timeframes: {timeframes}")
        results = {}
        
        for tf in timeframes:
            if tf not in self.supported_timeframes:
                logger.warning(f"⚠️ Unsupported timeframe: {tf}")
                continue
                
            tf_name = TIMEFRAMES[tf]['name']
            logger.info(f"[{tf_name}] Starting buffer initialization...")
            
            try:
                # Initialize CSV files and load historical data
                has_historical, has_live, wma_readiness = initialize_csv_files(tf)
                
                # Fetch additional data if needed
                if not wma_readiness['is_ready']:
                    logger.info(f"[{tf_name}] Fetching additional historical data...")
                    fetch_comprehensive_historical_data(tf)
                    wma_readiness = validate_wma_readiness(tf)
                
                results[tf] = {
                    'timeframe': tf_name,
                    'has_historical': has_historical,
                    'has_live': has_live,
                    'wma_ready': wma_readiness['is_ready'],
                    'candle_count': wma_readiness['current_candles'],
                    'required_candles': wma_readiness['required_candles'],
                    'readiness_percentage': wma_readiness['readiness_percentage']
                }
                
                logger.info(f"[{tf_name}] ✅ Buffer initialization complete - "
                           f"Ready: {wma_readiness['is_ready']}, "
                           f"Candles: {wma_readiness['current_candles']}")
                
            except Exception as e:
                logger.error(f"[{tf_name}] ❌ Buffer initialization failed: {str(e)}")
                results[tf] = {'error': str(e)}
        
        return results
    
    def get_first_valid_crossover(self, timeframe: str, today_only: bool = True) -> Optional[Dict]:
        """
        🎯 GET FIRST VALID WMA CROSSOVER SIGNAL
        
        Returns the first valid WMA5/WMA10 crossover signal with exact timing
        """
        if timeframe not in self.supported_timeframes:
            logger.error(f"❌ Unsupported timeframe: {timeframe}")
            return None
            
        tf_name = TIMEFRAMES[timeframe]['name']
        logger.info(f"[{tf_name}] Searching for first valid crossover signal...")
        
        try:
            # Get current data from deque
            current_data = list(historical_data_storage[timeframe])
            
            if len(current_data) < 10:
                logger.warning(f"[{tf_name}] Insufficient data for crossover analysis")
                return None
            
            # Calculate WMA signals
            analysis_result = calculate_wma_signals(current_data)
            
            if not analysis_result or not analysis_result['signals']:
                logger.info(f"[{tf_name}] No crossover signals found")
                return None
            
            # Filter for today's signals if requested
            if today_only:
                today = get_current_time_ist().date()
                today_signals = []
                
                for signal in analysis_result['signals']:
                    signal_time = pd.to_datetime(signal['timestamp'])
                    if signal_time.date() == today:
                        today_signals.append(signal)
                
                if not today_signals:
                    logger.info(f"[{tf_name}] No crossover signals found for today")
                    return None
                
                first_signal = today_signals[0]
            else:
                first_signal = analysis_result['signals'][0]
            
            # Format the result
            result = {
                'timeframe': tf_name,
                'timestamp': first_signal['timestamp'],
                'signal_type': first_signal['signal'],
                'price_level': first_signal['price_level'],
                'wma5': round(first_signal['wma5'], 2),
                'wma10': round(first_signal['wma10'], 2),
                'candle_data': {
                    'open': first_signal['candle_data']['open'],
                    'high': first_signal['candle_data']['high'],
                    'low': first_signal['candle_data']['low'],
                    'close': first_signal['candle_data']['close'],
                    'volume': first_signal['candle_data']['volume']
                }
            }
            
            logger.info(f"[{tf_name}] ✅ First crossover: {result['signal_type']} at "
                       f"{result['timestamp']} (Price: {result['price_level']:.2f})")
            
            return result
            
        except Exception as e:
            logger.error(f"[{tf_name}] ❌ Error finding first crossover: {str(e)}")
            return None
    
    def list_demand_zones_after_down_crossover(self, timeframe: str) -> List[Dict]:
        """
        🏪 LIST ALL DEMAND ZONES FORMED AFTER DOWN CROSSOVERS
        
        Returns comprehensive list of demand zones with metadata
        """
        if timeframe not in self.supported_timeframes:
            logger.error(f"❌ Unsupported timeframe: {timeframe}")
            return []
            
        tf_name = TIMEFRAMES[timeframe]['name']
        logger.info(f"[{tf_name}] Searching for demand zones after DOWN crossovers...")
        
        try:
            # Check zones CSV file
            zones_folder = "Supply_Demand_Zones"
            zones_file = os.path.join(zones_folder, f'zones_{tf_name}.csv')
            
            if not os.path.exists(zones_file):
                logger.info(f"[{tf_name}] No zones file found")
                return []
            
            zones_df = pd.read_csv(zones_file)
            
            # Filter for demand zones that started with DOWN crossover
            demand_zones_after_down = zones_df[
                (zones_df['zone_type'] == 'DEMAND') &
                (zones_df['start_signal'] == 'DOWN')
            ]
            
            if demand_zones_after_down.empty:
                logger.info(f"[{tf_name}] No demand zones found after DOWN crossovers")
                return []
            
            # Format results
            results = []
            for _, zone in demand_zones_after_down.iterrows():
                result = {
                    'timeframe': tf_name,
                    'zone_type': zone['zone_type'],
                    'zone_level': zone['zone_level'],
                    'zone_strength': zone['zone_strength'],
                    'start_timestamp': zone['start_timestamp'],
                    'end_timestamp': zone['end_timestamp'],
                    'start_signal': zone['start_signal'],
                    'end_signal': zone['end_signal'],
                    'candle_count': zone['candle_count'],
                    'price_range': zone.get('price_range', 0)
                }
                results.append(result)
            
            logger.info(f"[{tf_name}] ✅ Found {len(results)} demand zones after DOWN crossovers")
            
            return results
            
        except Exception as e:
            logger.error(f"[{tf_name}] ❌ Error listing demand zones: {str(e)}")
            return []
    
    def export_wma_values_with_crossovers(self, timeframe: str, output_file: Optional[str] = None) -> str:
        """
        📊 EXPORT WMA VALUES WITH CROSSOVER MARKERS TO CSV
        
        Creates comprehensive CSV export with WMA data and crossover signals
        """
        if timeframe not in self.supported_timeframes:
            logger.error(f"❌ Unsupported timeframe: {timeframe}")
            return ""
            
        tf_name = TIMEFRAMES[timeframe]['name']
        
        if not output_file:
            timestamp = get_current_time_ist().strftime("%Y%m%d_%H%M%S")
            output_file = f"wma_export_{tf_name}_{timestamp}.csv"
        
        logger.info(f"[{tf_name}] Exporting WMA data to {output_file}...")
        
        try:
            # Get live data file
            live_file = get_output_filename(timeframe, 'live')
            
            if not os.path.exists(live_file):
                logger.error(f"[{tf_name}] Live data file not found")
                return ""
            
            # Read and process data
            df = pd.read_csv(live_file)
            
            if df.empty:
                logger.warning(f"[{tf_name}] No data to export")
                return ""
            
            # Select relevant columns for export
            export_columns = [
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'wma5', 'wma10', 'wma5_10'
            ]
            
            # Filter available columns
            available_columns = [col for col in export_columns if col in df.columns]
            export_df = df[available_columns].copy()
            
            # Add crossover markers
            if 'wma5_10' in export_df.columns:
                export_df['crossover_marker'] = export_df['wma5_10'].apply(
                    lambda x: '🔼' if x == 'UP' else '🔽' if x == 'DOWN' else '➡️'
                )
            
            # Save export file
            export_df.to_csv(output_file, index=False)
            
            logger.info(f"[{tf_name}] ✅ Exported {len(export_df)} rows to {output_file}")
            
            return output_file
            
        except Exception as e:
            logger.error(f"[{tf_name}] ❌ Error exporting WMA data: {str(e)}")
            return ""

    def get_zone_continuation_patterns(self, timeframe: str) -> List[Dict]:
        """
        🔄 DETECT ZONE CONTINUATION PATTERNS FROM PREVIOUS SESSION

        Identifies zones that continue from previous trading sessions
        """
        if timeframe not in self.supported_timeframes:
            logger.error(f"❌ Unsupported timeframe: {timeframe}")
            return []

        tf_name = TIMEFRAMES[timeframe]['name']
        logger.info(f"[{tf_name}] Detecting zone continuation patterns...")

        try:
            # Check for continuation files
            continuation_folder = "Tomorrow_Zone_Continuation"

            if not os.path.exists(continuation_folder):
                logger.info(f"[{tf_name}] No continuation folder found")
                return []

            # Look for recent continuation files
            current_date = get_current_time_ist().date()
            continuation_files = []

            # Check last 3 days for continuation patterns
            for days_back in range(3):
                check_date = current_date - datetime.timedelta(days=days_back)
                continuation_file = os.path.join(
                    continuation_folder,
                    f'zones_{tf_name}_{check_date.strftime("%Y%m%d")}.csv'
                )

                if os.path.exists(continuation_file):
                    continuation_files.append((check_date, continuation_file))

            if not continuation_files:
                logger.info(f"[{tf_name}] No continuation files found")
                return []

            # Process continuation patterns
            patterns = []

            for date, file_path in continuation_files:
                try:
                    df = pd.read_csv(file_path)

                    for _, zone in df.iterrows():
                        pattern = {
                            'timeframe': tf_name,
                            'continuation_date': date.strftime("%Y-%m-%d"),
                            'zone_type': zone['zone_type'],
                            'zone_level': zone['zone_level'],
                            'zone_strength': zone['zone_strength'],
                            'original_start': zone['start_timestamp'],
                            'original_end': zone['end_timestamp'],
                            'candle_count': zone['candle_count'],
                            'is_active': self._check_zone_still_active(zone, timeframe)
                        }
                        patterns.append(pattern)

                except Exception as e:
                    logger.warning(f"[{tf_name}] Error reading continuation file {file_path}: {e}")

            logger.info(f"[{tf_name}] ✅ Found {len(patterns)} zone continuation patterns")

            return patterns

        except Exception as e:
            logger.error(f"[{tf_name}] ❌ Error detecting continuation patterns: {str(e)}")
            return []

    def _check_zone_still_active(self, zone: Dict, timeframe: str) -> bool:
        """Check if a zone is still active based on recent price action"""
        try:
            # Get recent data
            current_data = list(historical_data_storage[timeframe])

            if len(current_data) < 5:
                return False

            # Check last 5 candles for zone interaction
            recent_candles = current_data[-5:]
            zone_level = float(zone['zone_level'])
            zone_type = zone['zone_type']

            for candle in recent_candles:
                high = float(candle['high'])
                low = float(candle['low'])

                # Check if price has interacted with the zone
                if zone_type == 'SUPPLY' and high >= zone_level:
                    return True
                elif zone_type == 'DEMAND' and low <= zone_level:
                    return True

            return False

        except Exception:
            return False

    def verify_wma_calculation_accuracy(self, timeframe: str, candle_count: int = 10) -> Dict:
        """
        ✅ VERIFY WMA CALCULATION ACCURACY

        Compares WMA calculations with manual verification
        """
        if timeframe not in self.supported_timeframes:
            logger.error(f"❌ Unsupported timeframe: {timeframe}")
            return {}

        tf_name = TIMEFRAMES[timeframe]['name']
        logger.info(f"[{tf_name}] Verifying WMA calculation accuracy for last {candle_count} candles...")

        try:
            # Get current data
            current_data = list(historical_data_storage[timeframe])

            if len(current_data) < candle_count + 10:
                logger.warning(f"[{tf_name}] Insufficient data for verification")
                return {}

            # Get last N candles for verification
            verification_data = current_data[-candle_count:]

            # Calculate WMA using the system
            analysis_result = calculate_wma_signals(current_data)

            if not analysis_result:
                logger.error(f"[{tf_name}] Failed to calculate WMA for verification")
                return {}

            system_wma5 = analysis_result['wma5'][-candle_count:]
            system_wma10 = analysis_result['wma10'][-candle_count:]

            # Manual WMA calculation for verification
            close_prices = [float(candle['close']) for candle in verification_data]
            manual_wma5 = self._calculate_manual_wma(close_prices, 5)
            manual_wma10 = self._calculate_manual_wma(close_prices, 10)

            # Compare results
            verification_results = {
                'timeframe': tf_name,
                'candles_verified': candle_count,
                'wma5_accuracy': self._calculate_accuracy(system_wma5, manual_wma5),
                'wma10_accuracy': self._calculate_accuracy(system_wma10, manual_wma10),
                'max_wma5_difference': max(abs(s - m) for s, m in zip(system_wma5, manual_wma5) if not pd.isna(s) and not pd.isna(m)),
                'max_wma10_difference': max(abs(s - m) for s, m in zip(system_wma10, manual_wma10) if not pd.isna(s) and not pd.isna(m)),
                'verification_passed': True
            }

            # Check if verification passed (differences should be minimal)
            if (verification_results['max_wma5_difference'] > 0.01 or
                verification_results['max_wma10_difference'] > 0.01):
                verification_results['verification_passed'] = False
                logger.warning(f"[{tf_name}] ⚠️ WMA calculation accuracy issues detected")
            else:
                logger.info(f"[{tf_name}] ✅ WMA calculation accuracy verified")

            return verification_results

        except Exception as e:
            logger.error(f"[{tf_name}] ❌ Error verifying WMA accuracy: {str(e)}")
            return {}

    def _calculate_manual_wma(self, prices: List[float], period: int) -> List[float]:
        """Calculate WMA manually for verification"""
        if len(prices) < period:
            return [float('nan')] * len(prices)

        wma_values = []

        for i in range(len(prices)):
            if i < period - 1:
                wma_values.append(float('nan'))
            else:
                # Calculate weighted average
                weights = list(range(1, period + 1))
                weighted_sum = sum(w * prices[i - period + 1 + j] for j, w in enumerate(weights))
                weight_sum = sum(weights)
                wma = weighted_sum / weight_sum
                wma_values.append(wma)

        return wma_values

    def _calculate_accuracy(self, system_values: List[float], manual_values: List[float]) -> float:
        """Calculate accuracy percentage between system and manual calculations"""
        try:
            valid_pairs = [(s, m) for s, m in zip(system_values, manual_values)
                          if not pd.isna(s) and not pd.isna(m)]

            if not valid_pairs:
                return 0.0

            differences = [abs(s - m) for s, m in valid_pairs]
            avg_difference = sum(differences) / len(differences)
            avg_value = sum(s for s, m in valid_pairs) / len(valid_pairs)

            accuracy = max(0, 100 - (avg_difference / avg_value * 100))
            return round(accuracy, 2)

        except Exception:
            return 0.0
