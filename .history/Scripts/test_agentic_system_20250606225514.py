#!/usr/bin/env python3
"""
🧪 COMPREHENSIVE TEST SUITE FOR AGENTIC TRADING ASSISTANT
Advanced Testing Framework for WMA Crossover & Zone Detection System

This module provides comprehensive testing for all components of the
Indian Market Live Trading Assistant system.

🎯 TEST COVERAGE:
- WMA calculation accuracy
- Crossover detection precision
- Zone creation and validation
- Data integrity checks
- Performance benchmarks
- API integration tests

🔥 USAGE:
python test_agentic_system.py --run-all
python test_agentic_system.py --test-wma --timeframe 5
python test_agentic_system.py --test-zones --timeframe 1
python test_agentic_system.py --benchmark --iterations 100
"""

import os
import sys
import time
import argparse
import datetime
import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Tuple

# Add Scripts directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import trading system components
from multi_timeframe_live_fetcher import (
    TIMEFRAMES, calculate_wma, calculate_wma_signals, 
    historical_data_storage, validate_wma_readiness,
    initialize_csv_files, get_current_time_ist
)
from agentic_trading_assistant import AgenticTradingAssistant

# Configure test logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - [TEST] - %(message)s',
    handlers=[
        logging.FileHandler('test_results.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AgenticSystemTester:
    """
    🧪 COMPREHENSIVE TESTING FRAMEWORK
    
    Advanced testing suite for the Agentic Trading Assistant
    """
    
    def __init__(self):
        self.assistant = AgenticTradingAssistant()
        self.test_results = {}
        logger.info("🧪 Agentic System Tester initialized")
    
    def test_wma_calculation_accuracy(self, timeframe: str = '5') -> Dict:
        """
        📊 TEST WMA CALCULATION ACCURACY
        
        Validates WMA calculations against known mathematical results
        """
        logger.info(f"🧪 Testing WMA calculation accuracy for {timeframe}min timeframe...")
        
        # Create test data with known WMA values
        test_prices = [100.0, 101.0, 102.0, 103.0, 104.0, 105.0, 106.0, 107.0, 108.0, 109.0, 110.0]
        
        # Calculate WMA5 and WMA10
        wma5_result = calculate_wma(test_prices, 5)
        wma10_result = calculate_wma(test_prices, 10)
        
        # Manual calculation for verification
        expected_wma5_last = (107*1 + 108*2 + 109*3 + 110*4 + 110*5) / (1+2+3+4+5)  # Last WMA5
        expected_wma10_last = sum(test_prices[i] * (i+1) for i in range(10)) / sum(range(1, 11))  # Last WMA10
        
        # Validate results
        actual_wma5_last = wma5_result[-1] if not np.isnan(wma5_result[-1]) else 0
        actual_wma10_last = wma10_result[-1] if not np.isnan(wma10_result[-1]) else 0
        
        wma5_accuracy = abs(actual_wma5_last - expected_wma5_last) < 0.001
        wma10_accuracy = abs(actual_wma10_last - expected_wma10_last) < 0.001
        
        test_result = {
            'test_name': 'WMA Calculation Accuracy',
            'timeframe': timeframe,
            'wma5_passed': wma5_accuracy,
            'wma10_passed': wma10_accuracy,
            'wma5_expected': expected_wma5_last,
            'wma5_actual': actual_wma5_last,
            'wma10_expected': expected_wma10_last,
            'wma10_actual': actual_wma10_last,
            'overall_passed': wma5_accuracy and wma10_accuracy
        }
        
        self.test_results['wma_accuracy'] = test_result
        
        if test_result['overall_passed']:
            logger.info("✅ WMA calculation accuracy test PASSED")
        else:
            logger.error("❌ WMA calculation accuracy test FAILED")
        
        return test_result
    
    def test_crossover_detection(self, timeframe: str = '5') -> Dict:
        """
        🎯 TEST CROSSOVER DETECTION LOGIC
        
        Validates crossover signal detection with synthetic data
        """
        logger.info(f"🧪 Testing crossover detection for {timeframe}min timeframe...")
        
        # Create synthetic data with known crossovers
        base_price = 25000
        test_data = []
        
        # Generate data that should create specific crossovers
        prices = [
            # Initial trend down (WMA5 < WMA10)
            base_price, base_price-10, base_price-20, base_price-30, base_price-40,
            base_price-50, base_price-60, base_price-70, base_price-80, base_price-90,
            # Trend reversal up (should create UP crossover)
            base_price-85, base_price-75, base_price-60, base_price-40, base_price-15,
            base_price+10, base_price+35, base_price+65, base_price+100, base_price+140,
            # Trend reversal down (should create DOWN crossover)
            base_price+130, base_price+110, base_price+85, base_price+55, base_price+20
        ]
        
        # Create candle data
        for i, price in enumerate(prices):
            candle = {
                'timestamp': f"2025-06-06 09:{15+i:02d}:00",
                'open': price - 2,
                'high': price + 5,
                'low': price - 5,
                'close': price,
                'volume': 1000
            }
            test_data.append(candle)
        
        # Analyze for crossovers
        analysis_result = calculate_wma_signals(test_data)
        
        # Validate results
        has_signals = analysis_result and len(analysis_result['signals']) > 0
        has_up_signal = any(signal['signal'] == 'UP' for signal in analysis_result['signals']) if has_signals else False
        has_down_signal = any(signal['signal'] == 'DOWN' for signal in analysis_result['signals']) if has_signals else False
        
        test_result = {
            'test_name': 'Crossover Detection',
            'timeframe': timeframe,
            'signals_detected': len(analysis_result['signals']) if has_signals else 0,
            'up_signal_detected': has_up_signal,
            'down_signal_detected': has_down_signal,
            'expected_signals': 2,  # Should detect both UP and DOWN
            'overall_passed': has_up_signal and has_down_signal
        }
        
        self.test_results['crossover_detection'] = test_result
        
        if test_result['overall_passed']:
            logger.info("✅ Crossover detection test PASSED")
        else:
            logger.error("❌ Crossover detection test FAILED")
        
        return test_result
    
    def test_zone_creation(self, timeframe: str = '5') -> Dict:
        """
        🏪 TEST SUPPLY/DEMAND ZONE CREATION
        
        Validates zone creation logic with controlled data
        """
        logger.info(f"🧪 Testing zone creation for {timeframe}min timeframe...")
        
        # Create data that should generate zones
        base_price = 25000
        test_data = []
        
        # Generate data with clear zone formation patterns
        prices = [
            # Initial setup
            base_price, base_price-5, base_price-10, base_price-15, base_price-20,
            base_price-25, base_price-30, base_price-35, base_price-40, base_price-45,
            # UP crossover area (demand zone formation)
            base_price-40, base_price-30, base_price-15, base_price+5, base_price+25,
            base_price+50, base_price+80, base_price+115, base_price+155, base_price+200,
            # DOWN crossover area (supply zone formation)
            base_price+190, base_price+170, base_price+140, base_price+100, base_price+50
        ]
        
        # Create candle data with proper OHLC
        for i, price in enumerate(prices):
            candle = {
                'timestamp': f"2025-06-06 09:{15+i:02d}:00",
                'open': price - 1,
                'high': price + 10,
                'low': price - 10,
                'close': price,
                'volume': 1000
            }
            test_data.append(candle)
        
        # Analyze for zones
        analysis_result = calculate_wma_signals(test_data)
        
        # Validate zone creation
        has_zones = analysis_result and len(analysis_result['zones']) > 0
        has_supply_zone = any(zone['zone_type'] == 'SUPPLY' for zone in analysis_result['zones']) if has_zones else False
        has_demand_zone = any(zone['zone_type'] == 'DEMAND' for zone in analysis_result['zones']) if has_zones else False
        
        test_result = {
            'test_name': 'Zone Creation',
            'timeframe': timeframe,
            'zones_created': len(analysis_result['zones']) if has_zones else 0,
            'supply_zone_created': has_supply_zone,
            'demand_zone_created': has_demand_zone,
            'expected_zones': 1,  # Should create at least one zone
            'overall_passed': has_zones and (has_supply_zone or has_demand_zone)
        }
        
        self.test_results['zone_creation'] = test_result
        
        if test_result['overall_passed']:
            logger.info("✅ Zone creation test PASSED")
        else:
            logger.error("❌ Zone creation test FAILED")
        
        return test_result
    
    def test_data_integrity(self, timeframe: str = '5') -> Dict:
        """
        🔍 TEST DATA INTEGRITY AND VALIDATION
        
        Validates data consistency and error handling
        """
        logger.info(f"🧪 Testing data integrity for {timeframe}min timeframe...")
        
        # Test with various edge cases
        test_cases = [
            {'name': 'Empty Data', 'data': []},
            {'name': 'Insufficient Data', 'data': [{'close': 100, 'timestamp': '2025-06-06 09:15:00'}]},
            {'name': 'Invalid Prices', 'data': [{'close': 'invalid', 'timestamp': '2025-06-06 09:15:00'}] * 15},
            {'name': 'Missing Fields', 'data': [{'close': 100}] * 15}
        ]
        
        integrity_results = []
        
        for test_case in test_cases:
            try:
                result = calculate_wma_signals(test_case['data'])
                handled_gracefully = result is None or (isinstance(result, dict) and 'wma5' in result)
                integrity_results.append({
                    'case': test_case['name'],
                    'handled_gracefully': handled_gracefully,
                    'error_occurred': False
                })
            except Exception as e:
                integrity_results.append({
                    'case': test_case['name'],
                    'handled_gracefully': False,
                    'error_occurred': True,
                    'error': str(e)
                })
        
        # Overall integrity assessment
        all_handled = all(result['handled_gracefully'] for result in integrity_results)
        
        test_result = {
            'test_name': 'Data Integrity',
            'timeframe': timeframe,
            'test_cases': len(test_cases),
            'cases_handled': sum(1 for r in integrity_results if r['handled_gracefully']),
            'integrity_results': integrity_results,
            'overall_passed': all_handled
        }
        
        self.test_results['data_integrity'] = test_result
        
        if test_result['overall_passed']:
            logger.info("✅ Data integrity test PASSED")
        else:
            logger.error("❌ Data integrity test FAILED")
        
        return test_result
    
    def benchmark_performance(self, iterations: int = 100) -> Dict:
        """
        ⚡ BENCHMARK SYSTEM PERFORMANCE
        
        Measures processing speed and resource usage
        """
        logger.info(f"🧪 Benchmarking performance with {iterations} iterations...")
        
        # Create realistic test data
        base_price = 25000
        test_data = []
        
        for i in range(100):  # 100 candles for realistic testing
            price = base_price + np.random.normal(0, 50)  # Random walk
            candle = {
                'timestamp': f"2025-06-06 {9 + i//60:02d}:{15 + i%60:02d}:00",
                'open': price - 1,
                'high': price + 5,
                'low': price - 5,
                'close': price,
                'volume': 1000
            }
            test_data.append(candle)
        
        # Benchmark WMA calculation
        start_time = time.time()
        
        for _ in range(iterations):
            calculate_wma_signals(test_data)
        
        end_time = time.time()
        
        total_time = end_time - start_time
        avg_time_per_iteration = total_time / iterations
        calculations_per_second = 1 / avg_time_per_iteration if avg_time_per_iteration > 0 else 0
        
        # Performance thresholds
        acceptable_time_per_calc = 0.1  # 100ms per calculation
        performance_passed = avg_time_per_iteration < acceptable_time_per_calc
        
        test_result = {
            'test_name': 'Performance Benchmark',
            'iterations': iterations,
            'total_time': round(total_time, 4),
            'avg_time_per_iteration': round(avg_time_per_iteration, 4),
            'calculations_per_second': round(calculations_per_second, 2),
            'acceptable_threshold': acceptable_time_per_calc,
            'performance_passed': performance_passed,
            'overall_passed': performance_passed
        }
        
        self.test_results['performance'] = test_result
        
        if test_result['overall_passed']:
            logger.info("✅ Performance benchmark PASSED")
        else:
            logger.error("❌ Performance benchmark FAILED")
        
        return test_result
