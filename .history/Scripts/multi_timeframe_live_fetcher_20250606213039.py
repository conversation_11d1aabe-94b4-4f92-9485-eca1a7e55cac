#!/usr/bin/env python3
"""
🧠 AGENTIC INDIAN MARKET LIVE TRADING ASSISTANT
Multi-Timeframe WMA Crossover & Supply/Demand Zone Detection System

🎯 CORE CAPABILITIES:
- Real-time WMA crossover detection (5WMA vs 10WMA) across 1min, 5min, 60min
- Supply/demand zone identification and continuation tracking
- Rolling window indicators with seamless day-to-day continuity
- Market-aligned timestamp processing for NSE trading hours (9:15-15:29 IST)
- Intelligent historical data backfill for accurate indicator calculation

🔥 ENHANCED FEATURES:
- Start-of-day initialization with N-1 candle buffer loading
- Live crossover signal alerts with precise timing
- Zone continuation preparation for next trading session
- Comprehensive CSV output with trading indicators
- Retry logic for robust API data fetching
"""

import os
import time
import datetime
import pandas as pd
import numpy as np
import logging
import threading
from concurrent.futures import ThreadPoolExecutor
from collections import deque
from Dhan_Tradehull import Tradehull
import ta

# 📊 Enhanced Logging Configuration for Trading Assistant
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - [%(threadName)s] - %(message)s',
    handlers=[
        logging.FileHandler('agentic_trading_assistant.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 🔑 API Configuration (Tradehull/DhanHQ)
client_code = "1105577608"
token_id = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzUwOTU2MDc2LCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTEwNTU3NzYwOCJ9.suPPlPFFhOK_W4AumsLqIGMhF3Ez_rrFT4KF90Ndj3UruoRmOJ1AonS8BtFpYjWf4rP243mLO5HlWZqqn3XHDw"

# Initialize Tradehull client
tsl = Tradehull(client_code, token_id)

# 📈 Trading Configuration for Indian Market
SYMBOL = 'NIFTY'
EXCHANGE = 'INDEX'

# ⏰ NSE Market Timing Configuration (IST)
IST = datetime.timezone(datetime.timedelta(hours=5, minutes=30))  # UTC+5:30
MARKET_OPEN_TIME = datetime.time(9, 15)  # Market opens at 9:15 AM IST
MARKET_CLOSE_TIME = datetime.time(15, 29)  # Market closes at 3:29 PM IST (updated for accuracy)

# Timeframe configurations with optimized buffer times and historical data requirements
TIMEFRAMES = {
    '1': {
        'name': '1min',
        'interval_minutes': 1,
        'interval_seconds': 60,  # 1 minute in seconds
        'buffer_seconds': 10,  # 10 seconds after candle completion
        'wait_interval': 60,   # Check every minute
        'historical_days': 3,  # 3 days of historical data
        'max_candles': 3 * 375  # 3 days * 375 candles per day (9:15-15:30)
    },
    '5': {
        'name': '5min',
        'interval_minutes': 5,
        'interval_seconds': 300,  # 5 minutes in seconds
        'buffer_seconds': 20,  # 30 seconds after candle completion
        'wait_interval': 60,   # Check every minute
        'historical_days': 7,  # 7 days of historical data
        'max_candles': 7 * 75   # 7 days * 75 candles per day (375/5)
    },
    '60': {
        'name': '60min',
        'interval_minutes': 60,
        'interval_seconds': 3600,  # 60 minutes in seconds
        'buffer_seconds': 120, # 2 minutes after candle completion
        'wait_interval': 300,  # Check every 5 minutes
        'historical_days': 30, # 30 market days of historical data
        'max_candles': 30 * 6   # 30 days * 6 candles per day (375/60)
    }
}

# API configuration
MAX_RETRIES = 3
RETRY_DELAY = 2  # seconds

# Thread-safe lock for API calls
api_lock = threading.Lock()

# Global data storage using deque for efficient rolling windows
historical_data_storage = {
    '1': deque(maxlen=TIMEFRAMES['1']['max_candles']),
    '5': deque(maxlen=TIMEFRAMES['5']['max_candles']),
    '60': deque(maxlen=TIMEFRAMES['60']['max_candles'])
}

def get_current_time_ist():
    """Get current time in IST timezone"""
    return datetime.datetime.now(IST)

def is_market_open():
    """Check if market is currently open"""
    current_time = get_current_time_ist().time()
    current_date = get_current_time_ist().date()
    
    # Check if it's a weekday (Monday=0, Sunday=6)
    is_weekday = current_date.weekday() < 5
    
    # Check if current time is within market hours
    is_trading_hours = MARKET_OPEN_TIME <= current_time <= MARKET_CLOSE_TIME
    
    return is_weekday and is_trading_hours

def get_output_filename(timeframe, file_type='live'):
    """Generate output filename for timeframe with proper folder structure"""
    tf_name = TIMEFRAMES[timeframe]["name"]

    if file_type == 'historical':
        # Create Historical Data Layer folder structure
        folder = "Historical_Data_Layer"
        if not os.path.exists(folder):
            os.makedirs(folder)
        return os.path.join(folder, f'historical_{tf_name}.csv')
    else:
        # Create Live Data Layer folder structure
        folder = "Live_Data_Layer"
        if not os.path.exists(folder):
            os.makedirs(folder)
        return os.path.join(folder, f'{SYMBOL}_{EXCHANGE}_{tf_name}_live.csv')

def get_market_days_back(days):
    """Calculate the date that is 'days' market days back from today"""
    current_date = get_current_time_ist().date()
    market_days_count = 0
    check_date = current_date

    logger.info(f"Calculating {days} market days back from {current_date}")

    while market_days_count < days:
        check_date -= datetime.timedelta(days=1)
        # Check if it's a weekday (Monday=0, Sunday=6)
        if check_date.weekday() < 5:  # Monday to Friday
            market_days_count += 1
            logger.debug(f"Market day {market_days_count}: {check_date}")

    logger.info(f"Start date for {days} market days: {check_date}")
    return check_date

def api_call_with_retry(tf_key, func, **kwargs):
    """Execute API call with retry logic and thread safety"""
    with api_lock:  # Ensure thread-safe API calls
        for attempt in range(MAX_RETRIES):
            try:
                logger.debug(f"[{TIMEFRAMES[tf_key]['name']}] API call attempt {attempt + 1}/{MAX_RETRIES}")
                result = func(**kwargs)

                if result is not None and not result.empty:
                    logger.debug(f"[{TIMEFRAMES[tf_key]['name']}] API call successful on attempt {attempt + 1}")
                    return result
                else:
                    logger.warning(f"[{TIMEFRAMES[tf_key]['name']}] API call returned empty data on attempt {attempt + 1}")

            except Exception as e:
                logger.error(f"[{TIMEFRAMES[tf_key]['name']}] API call failed on attempt {attempt + 1}: {str(e)}")

            if attempt < MAX_RETRIES - 1:
                logger.info(f"[{TIMEFRAMES[tf_key]['name']}] Retrying in {RETRY_DELAY} seconds...")
                time.sleep(RETRY_DELAY)

        logger.error(f"[{TIMEFRAMES[tf_key]['name']}] All {MAX_RETRIES} API call attempts failed")
        return pd.DataFrame()

def initialize_csv_files(timeframe):
    """Initialize both live and historical CSV files for timeframe"""
    tf_name = TIMEFRAMES[timeframe]['name']

    # Initialize live data file
    live_file = get_output_filename(timeframe, 'live')
    historical_file = get_output_filename(timeframe, 'historical')

    # Create files if they don't exist
    for file_path, file_type in [(live_file, 'live'), (historical_file, 'historical')]:
        if not os.path.exists(file_path):
            headers = pd.DataFrame(columns=['open', 'high', 'low', 'close', 'volume', 'timestamp'])
            headers.to_csv(file_path, index=False)
            logger.info(f"[{tf_name}] Created new {file_type} file: {file_path}")

    # Load existing historical data into deque
    has_historical_data = load_historical_data_to_deque(timeframe)

    # Check live data
    has_live_data = False
    try:
        if os.path.exists(live_file):
            existing_data = pd.read_csv(live_file)
            if not existing_data.empty:
                existing_data['timestamp'] = pd.to_datetime(existing_data['timestamp'])
                today = get_current_time_ist().date()
                today_data = existing_data[existing_data['timestamp'].dt.date == today]

                if not today_data.empty:
                    logger.info(f"[{tf_name}] Found existing live file with {len(today_data)} candles from today")
                    has_live_data = True
    except Exception as e:
        logger.warning(f"[{tf_name}] Error reading existing live file: {e}")

    return has_historical_data, has_live_data

def load_historical_data_to_deque(timeframe):
    """Load existing historical data into deque"""
    tf_name = TIMEFRAMES[timeframe]['name']
    historical_file = get_output_filename(timeframe, 'historical')

    try:
        if os.path.exists(historical_file):
            df = pd.read_csv(historical_file)
            if not df.empty:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                # Sort by timestamp to ensure proper order
                df = df.sort_values('timestamp')

                # Clear existing deque and load data
                historical_data_storage[timeframe].clear()
                for _, row in df.iterrows():
                    historical_data_storage[timeframe].append(row.to_dict())

                logger.info(f"[{tf_name}] Loaded {len(df)} historical candles into memory")
                logger.info(f"[{tf_name}] Historical data range: {df['timestamp'].min()} to {df['timestamp'].max()}")
                return True
    except Exception as e:
        logger.warning(f"[{tf_name}] Error loading historical data: {e}")

    return False

def fetch_comprehensive_historical_data(timeframe):
    """Fetch comprehensive historical data based on timeframe requirements"""
    tf_name = TIMEFRAMES[timeframe]['name']
    historical_days = TIMEFRAMES[timeframe]['historical_days']

    logger.info(f"[{tf_name}] Fetching {historical_days} {'market days' if timeframe == '60' else 'calendar days'} of historical data...")

    # Calculate start date based on market days
    if timeframe == '60':  # 30 market days for 60min
        start_date = get_market_days_back(historical_days)
        logger.info(f"[{tf_name}] Using market days calculation: {historical_days} market days = from {start_date}")
    else:  # Calendar days for 1min and 5min
        start_date = get_current_time_ist().date() - datetime.timedelta(days=historical_days)
        logger.info(f"[{tf_name}] Using calendar days calculation: {historical_days} days = from {start_date}")

    logger.info(f"[{tf_name}] Fetching data from {start_date} to today")

    # Fetch the historical data with retry logic
    chart = api_call_with_retry(
        timeframe,
        tsl.get_historical_data,
        tradingsymbol=SYMBOL,
        exchange=EXCHANGE,
        timeframe=timeframe
    )

    if chart.empty:
        logger.warning(f"[{tf_name}] No historical data received from API")
        return False

    logger.info(f"[{tf_name}] Received historical data with {len(chart)} rows")

    # Convert timestamp to datetime for comparison
    chart['timestamp'] = pd.to_datetime(chart['timestamp'])

    # Normalize all timestamps to be timezone-naive for consistent comparison
    if chart['timestamp'].dt.tz is not None:
        chart['timestamp'] = chart['timestamp'].dt.tz_convert(IST)
        chart['timestamp'] = chart['timestamp'].dt.tz_localize(None)

    # Filter data from start_date onwards
    start_datetime = datetime.datetime.combine(start_date, MARKET_OPEN_TIME)
    current_time = get_current_time_ist().replace(tzinfo=None)

    # Filter chart data for the required period
    filtered_data = chart[
        (chart['timestamp'] >= start_datetime) &
        (chart['timestamp'] <= current_time)
    ].copy()

    if filtered_data.empty:
        logger.warning(f"[{tf_name}] No data found for the specified period")
        # For 60min, try to get all available data if filtered data is empty
        if timeframe == '60':
            logger.info(f"[{tf_name}] Trying to get all available data for 60min timeframe...")
            filtered_data = chart.copy()
            if not filtered_data.empty:
                logger.info(f"[{tf_name}] Using all available data: {len(filtered_data)} candles")
            else:
                return False
        else:
            return False

    # Sort by timestamp
    filtered_data = filtered_data.sort_values('timestamp')

    # For 60min timeframe, ensure we have enough data by taking the most recent candles
    if timeframe == '60':
        max_candles = TIMEFRAMES[timeframe]['max_candles']
        if len(filtered_data) > max_candles:
            filtered_data = filtered_data.tail(max_candles)
            logger.info(f"[{tf_name}] Limited to most recent {max_candles} candles for rolling window")

    logger.info(f"[{tf_name}] Found {len(filtered_data)} candles for {historical_days} {'market days' if timeframe == '60' else 'calendar days'}")
    logger.info(f"[{tf_name}] Data range: {filtered_data['timestamp'].min()} to {filtered_data['timestamp'].max()}")

    # Save to historical CSV file
    historical_file = get_output_filename(timeframe, 'historical')
    filtered_data.to_csv(historical_file, index=False)
    logger.info(f"[{tf_name}] ✅ Saved {len(filtered_data)} historical candles to {historical_file}")

    # Load into deque for efficient access
    historical_data_storage[timeframe].clear()
    for _, row in filtered_data.iterrows():
        historical_data_storage[timeframe].append(row.to_dict())

    # Show first and last few candles
    logger.info(f"[{tf_name}] First 3 candles:")
    for _, candle in filtered_data.head(3).iterrows():
        logger.info(f"[{tf_name}]   {candle['timestamp'].strftime('%Y-%m-%d %H:%M')}: O={candle['open']:.2f}, H={candle['high']:.2f}, L={candle['low']:.2f}, C={candle['close']:.2f}")

    logger.info(f"[{tf_name}] Last 3 candles:")
    for _, candle in filtered_data.tail(3).iterrows():
        logger.info(f"[{tf_name}]   {candle['timestamp'].strftime('%Y-%m-%d %H:%M')}: O={candle['open']:.2f}, H={candle['high']:.2f}, L={candle['low']:.2f}, C={candle['close']:.2f}")

    return True

def fetch_and_process_today_data(timeframe):
    """Fetch today's data for live CSV file"""
    tf_name = TIMEFRAMES[timeframe]['name']
    logger.info(f"[{tf_name}] Fetching today's data for live file...")

    # Fetch the historical data with retry logic
    chart = api_call_with_retry(
        timeframe,
        tsl.get_historical_data,
        tradingsymbol=SYMBOL,
        exchange=EXCHANGE,
        timeframe=timeframe
    )

    if chart.empty:
        logger.warning(f"[{tf_name}] No data received from API")
        return False

    # Convert timestamp to datetime for comparison
    chart['timestamp'] = pd.to_datetime(chart['timestamp'])

    # Normalize all timestamps to be timezone-naive for consistent comparison
    if chart['timestamp'].dt.tz is not None:
        chart['timestamp'] = chart['timestamp'].dt.tz_convert(IST)
        chart['timestamp'] = chart['timestamp'].dt.tz_localize(None)

    # Get today's date
    today = get_current_time_ist().date()

    # Filter data for today only (from market open to current time)
    today_start = datetime.datetime.combine(today, MARKET_OPEN_TIME)
    current_time = get_current_time_ist().replace(tzinfo=None)

    # Filter chart data for today's trading session
    today_data = chart[
        (chart['timestamp'] >= today_start) &
        (chart['timestamp'] <= current_time)
    ].copy()

    if today_data.empty:
        logger.warning(f"[{tf_name}] No data found for today's trading session")
        return False

    logger.info(f"[{tf_name}] Found {len(today_data)} candles for today's session")

    # Save to live CSV file (overwrite existing file)
    live_file = get_output_filename(timeframe, 'live')
    today_data.to_csv(live_file, index=False)
    logger.info(f"[{tf_name}] ✅ Saved {len(today_data)} today's candles to {live_file}")

    return True

def should_fetch_candle(timeframe, current_time):
    """Determine if we should fetch a candle for this timeframe at current time"""
    interval_minutes = TIMEFRAMES[timeframe]['interval_minutes']

    # For 1min: fetch every minute
    if interval_minutes == 1:
        return True

    # For 5min: fetch at 5, 10, 15, 20, etc. minute marks
    if interval_minutes == 5:
        return current_time.minute % 5 == 0

    # For 60min: fetch at the top of each hour
    if interval_minutes == 60:
        return current_time.minute == 0

    return False

def get_target_candle_time(timeframe, current_time):
    """Get the target candle time based on timeframe"""
    interval_minutes = TIMEFRAMES[timeframe]['interval_minutes']
    current_timestamp = pd.Timestamp(current_time)

    if interval_minutes == 1:
        # For 1min: get previous minute
        return current_timestamp - pd.Timedelta(minutes=1)
    elif interval_minutes == 5:
        # For 5min: get the previous 5-minute boundary
        minutes_past = current_time.minute % 5
        if minutes_past == 0:
            # If exactly on boundary, get previous 5min candle
            return current_timestamp - pd.Timedelta(minutes=5)
        else:
            # Round down to previous 5min boundary
            return current_timestamp.floor('5min')
    elif interval_minutes == 60:
        # For 60min: get previous hour
        if current_time.minute == 0:
            return current_timestamp - pd.Timedelta(hours=1)
        else:
            return current_timestamp.floor('H')

    return current_timestamp

def find_and_save_candle(timeframe, target_time):
    """
    🔥 ENHANCED LIVE CANDLE PROCESSING WITH CROSSOVER DETECTION

    This function:
    1. Fetches new candle data from API
    2. Saves to live CSV file
    3. Updates historical deque storage
    4. 🆕 PERFORMS LIVE CROSSOVER DETECTION for new candles
    5. 🆕 UPDATES CSV with WMA5_10 column
    6. 🆕 LOGS crossover signals for trading alerts

    Args:
        timeframe: The timeframe to process ('1', '5', '60')
        target_time: The target candle timestamp

    Returns:
        bool: True if candle was successfully processed, False otherwise
    """
    tf_name = TIMEFRAMES[timeframe]['name']
    logger.info(f"[{tf_name}] Fetching candle data for {target_time.strftime('%Y-%m-%d %H:%M:00')} IST...")

    # Fetch the historical data with retry logic
    chart = api_call_with_retry(
        timeframe,
        tsl.get_historical_data,
        tradingsymbol=SYMBOL,
        exchange=EXCHANGE,
        timeframe=timeframe
    )

    if chart.empty:
        logger.warning(f"[{tf_name}] No data received from API")
        return False

    logger.debug(f"[{tf_name}] Received data with {len(chart)} rows")

    # Convert timestamp to datetime for comparison
    chart['timestamp'] = pd.to_datetime(chart['timestamp'])

    # Normalize all timestamps to be timezone-naive for consistent comparison
    if chart['timestamp'].dt.tz is not None:
        chart['timestamp'] = chart['timestamp'].dt.tz_convert(IST)
        chart['timestamp'] = chart['timestamp'].dt.tz_localize(None)

    # Ensure target_time is timezone-naive
    target_timestamp = pd.Timestamp(target_time)
    if target_timestamp.tz is not None:
        target_timestamp = target_timestamp.tz_localize(None)

    # Find the candle for the target time (use appropriate floor based on timeframe)
    if timeframe == '1':
        target_candles = chart[chart['timestamp'].dt.floor('min') == target_timestamp.floor('min')]
    elif timeframe == '5':
        target_candles = chart[chart['timestamp'].dt.floor('5min') == target_timestamp.floor('5min')]
    elif timeframe == '60':
        target_candles = chart[chart['timestamp'].dt.floor('H') == target_timestamp.floor('H')]
    else:
        target_candles = chart[chart['timestamp'].dt.floor('min') == target_timestamp.floor('min')]

    if not target_candles.empty:
        candle_data = target_candles.iloc[0]
        candle_dict = candle_data.to_dict()

        # Check if this candle already exists in live CSV
        live_file = get_output_filename(timeframe, 'live')
        candle_exists = False

        if os.path.exists(live_file):
            existing_data = pd.read_csv(live_file)
            if not existing_data.empty:
                existing_data['timestamp'] = pd.to_datetime(existing_data['timestamp'])

                # Check for existing timestamp
                target_floor = target_timestamp.floor('5min' if timeframe == '5' else ('H' if timeframe == '60' else 'min'))
                existing_floors = existing_data['timestamp'].dt.floor('5min' if timeframe == '5' else ('H' if timeframe == '60' else 'min'))

                if target_floor in existing_floors.values:
                    logger.info(f"[{tf_name}] Candle for {target_time.strftime('%H:%M')} already exists in live file, skipping...")
                    candle_exists = True

        if not candle_exists:
            # Add to live CSV file (append mode)
            target_candles.to_csv(live_file, mode='a', header=False, index=False)
            logger.info(f"[{tf_name}] ✅ Added candle to live file for {target_time.strftime('%H:%M')}: "
                       f"O={candle_data['open']:.2f}, H={candle_data['high']:.2f}, "
                       f"L={candle_data['low']:.2f}, C={candle_data['close']:.2f}, "
                       f"V={candle_data['volume']}")

        # Add to historical deque (this will automatically maintain the rolling window)
        historical_data_storage[timeframe].append(candle_dict)

        # 🔥 LIVE CROSSOVER DETECTION: Analyze current data for WMA crossover signals
        process_live_crossover_detection(timeframe, candle_dict)

        # Update historical CSV file with current deque data
        update_historical_csv(timeframe)

        return True
    else:
        logger.warning(f"[{tf_name}] No candle data found for {target_time.strftime('%H:%M')}")

        # Show available timestamps for debugging
        available_times = chart['timestamp'].unique()
        logger.debug(f"[{tf_name}] Available timestamps: {len(available_times)} candles")

        if len(available_times) > 0:
            latest_time = available_times.max()
            logger.info(f"[{tf_name}] Most recent candle available: {latest_time}")

        return False

def process_live_crossover_detection(timeframe, new_candle):
    """
    🔥 ENHANCED LIVE CROSSOVER AND ZONE DETECTION FUNCTION

    This function processes each new live candle to:
    1. Detect new WMA crossover signals (UP/DOWN)
    2. Create supply/demand zones between consecutive crossovers
    3. Update live CSV with WMA values and zone indicators
    4. Save zones to dedicated CSV files
    5. Log crossover signals and zone creation for trading alerts

    Args:
        timeframe: The timeframe being processed ('1', '5', '60')
        new_candle: The newly received candle data
    """
    tf_name = TIMEFRAMES[timeframe]['name']

    try:
        # Get current historical data from deque (includes the new candle)
        if len(historical_data_storage[timeframe]) < 10:
            logger.debug(f"[{tf_name}] 📊 Not enough data for live analysis (need 10+ candles)")
            return

        # Convert deque to list for analysis
        current_data = list(historical_data_storage[timeframe])

        # 📈 STEP 1: Perform comprehensive WMA and zone analysis
        logger.debug(f"[{tf_name}] 🔍 Analyzing live data for crossovers and zones...")
        analysis_result = calculate_wma_signals(current_data)

        if not analysis_result:
            logger.debug(f"[{tf_name}] No analysis result from WMA calculation")
            return

        # 📊 STEP 2: Check for new crossover signals
        if analysis_result['signals']:
            latest_signal = analysis_result['signals'][-1]
            signal_time = latest_signal['timestamp']
            signal_type = latest_signal['signal']

            # Check if this is a new signal (within last few candles)
            new_candle_time = pd.to_datetime(new_candle['timestamp'])
            signal_time_dt = pd.to_datetime(signal_time)

            # If signal is from the current or recent candle, it's a new live signal
            time_diff = abs((new_candle_time - signal_time_dt).total_seconds())
            if time_diff <= TIMEFRAMES[timeframe]['interval_seconds'] * 2:  # Within 2 intervals
                logger.info(f"[{tf_name}] 🚨 NEW LIVE CROSSOVER: {signal_type} at {signal_time}")
                logger.info(f"[{tf_name}] 📊 WMA5: {latest_signal['wma5']:.2f}, WMA10: {latest_signal['wma10']:.2f}")

        # 🎯 STEP 3: Check for new supply/demand zones
        if analysis_result['zones']:
            latest_zone = analysis_result['zones'][-1]
            zone_end_time = pd.to_datetime(latest_zone['end_timestamp'])

            # Check if this is a newly created zone
            time_diff = abs((new_candle_time - zone_end_time).total_seconds())
            if time_diff <= TIMEFRAMES[timeframe]['interval_seconds'] * 2:  # Within 2 intervals
                logger.info(f"[{tf_name}] 🎯 NEW {latest_zone['zone_type']} ZONE CREATED!")
                logger.info(f"[{tf_name}] 📍 Zone Level: {latest_zone['zone_level']:.2f}")
                logger.info(f"[{tf_name}] 📊 Zone Strength: {latest_zone['zone_strength']}")
                logger.info(f"[{tf_name}] ⏱️ Duration: {latest_zone['candle_count']} candles")

                # Save new zones to CSV
                append_zones_to_csv(timeframe, [latest_zone])

        # 🔄 STEP 4: Update live CSV with comprehensive indicators
        update_live_csv_with_enhanced_indicators(timeframe, analysis_result)

    except Exception as e:
        logger.error(f"[{tf_name}] ❌ Error in live crossover and zone detection: {str(e)}")

def get_existing_zone_count(timeframe):
    """Get count of existing zones in CSV file"""
    tf_name = TIMEFRAMES[timeframe]['name']
    zones_file = os.path.join("Supply_Demand_Zones", f'zones_{tf_name}.csv')

    if os.path.exists(zones_file):
        try:
            existing_zones = pd.read_csv(zones_file)
            return len(existing_zones)
        except:
            return 0
    return 0

def update_live_csv_with_crossover_indicators(timeframe, analysis_result):
    """
    📊 UPDATE LIVE CSV WITH WMA5_10 COLUMN ONLY

    This function enhances the live CSV file with:
    - wma5_10 column: UP/DOWN/HOLD signals
    """
    tf_name = TIMEFRAMES[timeframe]['name']
    live_file = get_output_filename(timeframe, 'live')

    try:
        if not os.path.exists(live_file):
            logger.debug(f"[{tf_name}] Live CSV file doesn't exist yet")
            return

        # Read current live CSV
        df = pd.read_csv(live_file)
        if df.empty:
            return

        df['timestamp'] = pd.to_datetime(df['timestamp'])

        # Add WMA5_10 column if it doesn't exist
        if 'wma5_10' not in df.columns:
            df['wma5_10'] = 'HOLD'

        # Update with current analysis (crossovers only)
        enhanced_df = add_wma_crossover_columns(df.copy(), analysis_result)

        # Save enhanced live CSV
        enhanced_df.to_csv(live_file, index=False)
        logger.debug(f"[{tf_name}] ✅ Updated live CSV with crossover indicators")

    except Exception as e:
        logger.error(f"[{tf_name}] ❌ Error updating live CSV with crossover indicators: {str(e)}")

def update_live_csv_with_indicators(timeframe, analysis_result):
    """
    📊 UPDATE LIVE CSV WITH WMA5_10 AND PRICEACTIVE COLUMNS

    This function enhances the live CSV file with:
    - wma5_10 column: UP/DOWN/HOLD signals
    - PriceActive column: SUPPLY/DEMAND zone markers
    """
    tf_name = TIMEFRAMES[timeframe]['name']
    live_file = get_output_filename(timeframe, 'live')

    try:
        if not os.path.exists(live_file):
            logger.debug(f"[{tf_name}] Live CSV file doesn't exist yet")
            return

        # Read current live CSV
        df = pd.read_csv(live_file)
        if df.empty:
            return

        df['timestamp'] = pd.to_datetime(df['timestamp'])

        # Add enhanced columns if they don't exist
        if 'wma5_10' not in df.columns:
            df['wma5_10'] = 'HOLD'
        if 'PriceActive' not in df.columns:
            df['PriceActive'] = ''

        # Update with current analysis
        enhanced_df = add_wma_and_zone_columns(df.copy(), analysis_result)

        # Save enhanced live CSV
        enhanced_df.to_csv(live_file, index=False)
        logger.debug(f"[{tf_name}] ✅ Updated live CSV with indicators")

    except Exception as e:
        logger.error(f"[{tf_name}] ❌ Error updating live CSV with indicators: {str(e)}")

def update_live_csv_with_enhanced_indicators(timeframe, analysis_result):
    """
    🔥 ENHANCED CSV UPDATE WITH COMPREHENSIVE SUPPLY/DEMAND INDICATORS

    This function creates a comprehensive CSV output matching the reference format with:
    - Basic OHLCV data
    - WMA5 and WMA10 values
    - WMA5_10 crossover signals (UP/DOWN/HOLD)
    - Supply/Demand zone markers
    - Zone levels and metadata

    Output format matches: candle_data_with_price_action.csv
    """
    tf_name = TIMEFRAMES[timeframe]['name']
    live_file = get_output_filename(timeframe, 'live')

    try:
        if not os.path.exists(live_file):
            logger.debug(f"[{tf_name}] Live CSV file doesn't exist yet")
            return

        # Read current live CSV
        df = pd.read_csv(live_file)
        if df.empty:
            return

        df['timestamp'] = pd.to_datetime(df['timestamp'])

        # Add comprehensive columns matching reference format
        required_columns = {
            'wma5': 0.0,
            'wma10': 0.0,
            'wma5_10': 'HOLD',
            'supply_zone': '',
            'demand_zone': '',
            'zone_level': 0.0,
            'zone_strength': '',
            'PriceActive': ''
        }

        for col, default_val in required_columns.items():
            if col not in df.columns:
                df[col] = default_val

        # Update with enhanced analysis
        enhanced_df = add_enhanced_wma_and_zone_columns(df.copy(), analysis_result)

        # Save enhanced live CSV
        enhanced_df.to_csv(live_file, index=False)
        logger.debug(f"[{tf_name}] ✅ Updated live CSV with enhanced indicators")

    except Exception as e:
        logger.error(f"[{tf_name}] ❌ Error updating live CSV with enhanced indicators: {str(e)}")

def prepare_zones_for_tomorrow(timeframe, analysis_result):
    """
    📅 PREPARE ZONES FOR TOMORROW'S TRADING CONTINUATION

    This function:
    1. Identifies active zones that should continue tomorrow
    2. Saves zone continuation data for next trading session
    3. Ensures supply/demand levels are available for tomorrow's analysis
    """
    tf_name = TIMEFRAMES[timeframe]['name']

    try:
        if not analysis_result or not analysis_result['zones']:
            return

        # Create tomorrow's zones folder
        tomorrow_folder = "Tomorrow_Zone_Continuation"
        if not os.path.exists(tomorrow_folder):
            os.makedirs(tomorrow_folder)

        # Get current date for tomorrow's file
        current_date = get_current_time_ist().date()
        tomorrow_date = current_date + pd.Timedelta(days=1)

        tomorrow_file = os.path.join(tomorrow_folder, f'zones_{tf_name}_{tomorrow_date.strftime("%Y%m%d")}.csv')

        # Filter zones that should continue tomorrow (recent zones)
        continuation_zones = []

        for zone in analysis_result['zones']:
            zone_end_time = pd.to_datetime(zone['end_timestamp'])

            # Include zones from today that should be monitored tomorrow
            if zone_end_time.date() == current_date:
                continuation_zone = zone.copy()
                continuation_zone['continuation_date'] = tomorrow_date.strftime("%Y-%m-%d")
                continuation_zone['original_date'] = current_date.strftime("%Y-%m-%d")
                continuation_zones.append(continuation_zone)

        if continuation_zones:
            # Save tomorrow's continuation zones
            continuation_df = pd.DataFrame(continuation_zones)
            continuation_df.to_csv(tomorrow_file, index=False)

            logger.info(f"[{tf_name}] 📅 Prepared {len(continuation_zones)} zones for tomorrow's continuation")
            logger.info(f"[{tf_name}] 💾 Saved to: {tomorrow_file}")

    except Exception as e:
        logger.error(f"[{tf_name}] ❌ Error preparing zones for tomorrow: {str(e)}")

def calculate_wma(prices, period):
    """Calculate Weighted Moving Average using ta library"""
    return ta.trend.WMAIndicator(pd.Series(prices), window=period).wma().values

def calculate_wma_crossover_signals(data):
    """Calculate WMA crossover signals only (no zones)"""
    if len(data) < 10:  # Need at least 10 candles for 10 WMA
        return None

    # Convert to numpy arrays
    close_prices = np.array([float(candle['close']) for candle in data])

    # Calculate WMAs using custom function
    wma5 = calculate_wma(close_prices, 5)
    wma10 = calculate_wma(close_prices, 10)

    # Find crossover points
    signals = []

    for i in range(10, len(data)):  # Start from index 10 (after WMA10 is valid)
        current_wma5 = wma5[i]
        current_wma10 = wma10[i]
        prev_wma5 = wma5[i-1]
        prev_wma10 = wma10[i-1]

        # Skip if any WMA value is NaN
        if np.isnan(current_wma5) or np.isnan(current_wma10) or np.isnan(prev_wma5) or np.isnan(prev_wma10):
            continue

        # Detect crossovers
        signal_type = None

        # Up signal: 5 WMA crosses above 10 WMA
        if prev_wma5 <= prev_wma10 and current_wma5 > current_wma10:
            signal_type = 'UP'
        # Down signal: 5 WMA crosses below 10 WMA
        elif prev_wma5 >= prev_wma10 and current_wma5 < current_wma10:
            signal_type = 'DOWN'

        if signal_type:
            signal_data = {
                'index': i,
                'timestamp': data[i]['timestamp'],
                'signal': signal_type,
                'wma5': current_wma5,
                'wma10': current_wma10,
                'candle_data': data[i]
            }
            signals.append(signal_data)

    return {
        'signals': signals,
        'wma5': wma5,
        'wma10': wma10
    }

def calculate_wma_signals(data):
    """
    🔥 ENHANCED WMA SIGNALS AND SUPPLY/DEMAND ZONE DETECTION

    This function implements the complete supply and demand zone detection algorithm:
    1. Calculate 5-period and 10-period Weighted Moving Averages (WMA)
    2. Detect crossover points (UP: 5WMA crosses above 10WMA, DOWN: 5WMA crosses below 10WMA)
    3. Create supply/demand zones between consecutive crossovers:
       - SUPPLY ZONE: Between UP→DOWN crossovers (highest high in range)
       - DEMAND ZONE: Between DOWN→UP crossovers (lowest low in range)
    4. Include comprehensive zone metadata for analysis
    """
    if len(data) < 10:  # Need at least 10 candles for 10 WMA
        return None

    # Convert to numpy arrays for efficient calculation
    close_prices = np.array([float(candle['close']) for candle in data])
    high_prices = np.array([float(candle['high']) for candle in data])
    low_prices = np.array([float(candle['low']) for candle in data])

    # Calculate WMAs using custom function
    wma5 = calculate_wma(close_prices, 5)
    wma10 = calculate_wma(close_prices, 10)

    # Initialize tracking variables
    signals = []
    zones = []
    last_signal = None
    last_signal_index = None

    # Scan through data to detect crossovers and create zones
    for i in range(10, len(data)):  # Start from index 10 (after WMA10 is valid)
        current_wma5 = wma5[i]
        current_wma10 = wma10[i]
        prev_wma5 = wma5[i-1]
        prev_wma10 = wma10[i-1]

        # Skip if any WMA value is NaN
        if np.isnan(current_wma5) or np.isnan(current_wma10) or np.isnan(prev_wma5) or np.isnan(prev_wma10):
            continue

        # 📊 CROSSOVER DETECTION LOGIC
        signal_type = None

        # 🔼 UP signal: 5 WMA crosses above 10 WMA (Bullish crossover)
        if prev_wma5 <= prev_wma10 and current_wma5 > current_wma10:
            signal_type = 'UP'
        # 🔽 DOWN signal: 5 WMA crosses below 10 WMA (Bearish crossover)
        elif prev_wma5 >= prev_wma10 and current_wma5 < current_wma10:
            signal_type = 'DOWN'

        if signal_type:
            # Create signal data with comprehensive information
            signal_data = {
                'index': i,
                'timestamp': data[i]['timestamp'],
                'signal': signal_type,
                'wma5': current_wma5,
                'wma10': current_wma10,
                'candle_data': data[i],
                'price_level': data[i]['close']
            }
            signals.append(signal_data)

            # 🎯 SUPPLY/DEMAND ZONE CREATION
            # Create zone if we have a previous signal (need two consecutive crossovers)
            if last_signal and last_signal_index is not None:
                zone_data = create_enhanced_supply_demand_zone(
                    data, last_signal, signal_type, last_signal_index, i,
                    high_prices, low_prices, wma5, wma10
                )
                if zone_data:
                    zones.append(zone_data)

            # Update tracking variables for next iteration
            last_signal = signal_type
            last_signal_index = i

    return {
        'signals': signals,
        'zones': zones,
        'wma5': wma5,
        'wma10': wma10,
        'data_length': len(data)
    }

def create_supply_demand_zone(data, prev_signal, current_signal, start_idx, end_idx, high_prices, low_prices, signals):
    """Create supply or demand zone between two signals with crossover candle data"""

    # Get the crossover candle data
    start_candle = data[start_idx]
    end_candle = data[end_idx]

    # Supply zone: Between UP and DOWN signals (use highest high)
    if prev_signal == 'UP' and current_signal == 'DOWN':
        zone_high = np.max(high_prices[start_idx:end_idx+1])
        zone_type = 'SUPPLY'
        zone_level = zone_high

    # Demand zone: Between DOWN and UP signals (use lowest low)
    elif prev_signal == 'DOWN' and current_signal == 'UP':
        zone_low = np.min(low_prices[start_idx:end_idx+1])
        zone_type = 'DEMAND'
        zone_level = zone_low
    else:
        return None

    # Get WMA values from the most recent signals
    start_wma5 = signals[-2]['wma5'] if len(signals) >= 2 else None
    start_wma10 = signals[-2]['wma10'] if len(signals) >= 2 else None
    end_wma5 = signals[-1]['wma5'] if len(signals) >= 1 else None
    end_wma10 = signals[-1]['wma10'] if len(signals) >= 1 else None

    return {
        'zone_type': zone_type,
        'zone_level': zone_level,
        'start_timestamp': data[start_idx]['timestamp'],
        'end_timestamp': data[end_idx]['timestamp'],
        'start_signal': prev_signal,
        'end_signal': current_signal,
        'candle_count': end_idx - start_idx + 1,
        # Start crossover candle OHLC
        'start_open': start_candle['open'],
        'start_high': start_candle['high'],
        'start_low': start_candle['low'],
        'start_close': start_candle['close'],
        'start_volume': start_candle['volume'],
        'start_wma5': start_wma5,
        'start_wma10': start_wma10,
        # End crossover candle OHLC
        'end_open': end_candle['open'],
        'end_high': end_candle['high'],
        'end_low': end_candle['low'],
        'end_close': end_candle['close'],
        'end_volume': end_candle['volume'],
        'end_wma5': end_wma5,
        'end_wma10': end_wma10
    }

def create_enhanced_supply_demand_zone(data, prev_signal, current_signal, start_idx, end_idx, high_prices, low_prices, wma5, wma10):
    """
    🎯 ENHANCED SUPPLY/DEMAND ZONE CREATION

    Creates comprehensive supply and demand zones between consecutive WMA crossovers:

    SUPPLY ZONE LOGIC:
    - Created between UP→DOWN crossovers (bullish to bearish transition)
    - Zone level = Highest high in the price range between crossovers
    - Represents resistance area where selling pressure emerged

    DEMAND ZONE LOGIC:
    - Created between DOWN→UP crossovers (bearish to bullish transition)
    - Zone level = Lowest low in the price range between crossovers
    - Represents support area where buying pressure emerged

    Args:
        data: List of candle data dictionaries
        prev_signal: Previous crossover signal ('UP' or 'DOWN')
        current_signal: Current crossover signal ('UP' or 'DOWN')
        start_idx: Index of previous crossover candle
        end_idx: Index of current crossover candle
        high_prices: Array of high prices
        low_prices: Array of low prices
        wma5: Array of 5-period WMA values
        wma10: Array of 10-period WMA values

    Returns:
        Dictionary containing comprehensive zone data or None if invalid combination
    """

    # Validate crossover combination
    if not ((prev_signal == 'UP' and current_signal == 'DOWN') or
            (prev_signal == 'DOWN' and current_signal == 'UP')):
        return None

    # Get crossover candle data
    start_candle = data[start_idx]
    end_candle = data[end_idx]

    # 📈 SUPPLY ZONE: Between UP→DOWN crossovers (Resistance Zone)
    if prev_signal == 'UP' and current_signal == 'DOWN':
        # Find the highest high in the range (supply level)
        zone_range = high_prices[start_idx:end_idx+1]
        zone_level = np.max(zone_range)
        zone_level_idx = start_idx + np.argmax(zone_range)
        zone_type = 'SUPPLY'
        zone_description = 'Resistance zone where selling pressure emerged'

    # 📉 DEMAND ZONE: Between DOWN→UP crossovers (Support Zone)
    elif prev_signal == 'DOWN' and current_signal == 'UP':
        # Find the lowest low in the range (demand level)
        zone_range = low_prices[start_idx:end_idx+1]
        zone_level = np.min(zone_range)
        zone_level_idx = start_idx + np.argmin(zone_range)
        zone_type = 'DEMAND'
        zone_description = 'Support zone where buying pressure emerged'

    # Get the candle that created the zone level
    zone_level_candle = data[zone_level_idx]

    # Calculate zone strength metrics
    price_range = np.max(high_prices[start_idx:end_idx+1]) - np.min(low_prices[start_idx:end_idx+1])
    candle_count = end_idx - start_idx + 1

    # Get WMA values at crossover points
    start_wma5 = wma5[start_idx] if start_idx < len(wma5) else None
    start_wma10 = wma10[start_idx] if start_idx < len(wma10) else None
    end_wma5 = wma5[end_idx] if end_idx < len(wma5) else None
    end_wma10 = wma10[end_idx] if end_idx < len(wma10) else None
    zone_level_wma5 = wma5[zone_level_idx] if zone_level_idx < len(wma5) else None
    zone_level_wma10 = wma10[zone_level_idx] if zone_level_idx < len(wma10) else None

    return {
        # Zone identification
        'zone_type': zone_type,
        'zone_level': zone_level,
        'zone_description': zone_description,
        'zone_strength': 'HIGH' if candle_count >= 5 else 'MEDIUM' if candle_count >= 3 else 'LOW',

        # Zone boundaries
        'start_timestamp': start_candle['timestamp'],
        'end_timestamp': end_candle['timestamp'],
        'zone_level_timestamp': zone_level_candle['timestamp'],
        'candle_count': candle_count,
        'price_range': price_range,

        # Crossover signals
        'start_signal': prev_signal,
        'end_signal': current_signal,

        # Start crossover candle (where zone begins)
        'start_open': start_candle['open'],
        'start_high': start_candle['high'],
        'start_low': start_candle['low'],
        'start_close': start_candle['close'],
        'start_volume': start_candle['volume'],
        'start_wma5': start_wma5,
        'start_wma10': start_wma10,

        # End crossover candle (where zone ends)
        'end_open': end_candle['open'],
        'end_high': end_candle['high'],
        'end_low': end_candle['low'],
        'end_close': end_candle['close'],
        'end_volume': end_candle['volume'],
        'end_wma5': end_wma5,
        'end_wma10': end_wma10,

        # Zone level candle (where the actual supply/demand level was created)
        'zone_level_open': zone_level_candle['open'],
        'zone_level_high': zone_level_candle['high'],
        'zone_level_low': zone_level_candle['low'],
        'zone_level_close': zone_level_candle['close'],
        'zone_level_volume': zone_level_candle['volume'],
        'zone_level_wma5': zone_level_wma5,
        'zone_level_wma10': zone_level_wma10
    }

def append_zones_to_csv(timeframe, zones):
    """Append supply/demand zones to CSV files"""
    if not zones:
        return

    tf_name = TIMEFRAMES[timeframe]['name']

    # Create zones CSV filename
    zones_folder = "Supply_Demand_Zones"
    if not os.path.exists(zones_folder):
        os.makedirs(zones_folder)

    zones_file = os.path.join(zones_folder, f'zones_{tf_name}.csv')

    # Create zones DataFrame
    zones_df = pd.DataFrame(zones)

    # Check if file exists and append or create
    if os.path.exists(zones_file):
        # Read existing data to avoid duplicates
        existing_zones = pd.read_csv(zones_file)
        if not existing_zones.empty:
            existing_zones['start_timestamp'] = pd.to_datetime(existing_zones['start_timestamp'])
            existing_zones['end_timestamp'] = pd.to_datetime(existing_zones['end_timestamp'])

            # Filter out zones that already exist
            zones_df['start_timestamp'] = pd.to_datetime(zones_df['start_timestamp'])
            zones_df['end_timestamp'] = pd.to_datetime(zones_df['end_timestamp'])

            # Check for duplicates based on timestamps and zone type
            new_zones = []
            for _, zone in zones_df.iterrows():
                is_duplicate = False
                for _, existing in existing_zones.iterrows():
                    if (zone['start_timestamp'] == existing['start_timestamp'] and
                        zone['end_timestamp'] == existing['end_timestamp'] and
                        zone['zone_type'] == existing['zone_type']):
                        is_duplicate = True
                        break

                if not is_duplicate:
                    new_zones.append(zone.to_dict())

            if new_zones:
                new_zones_df = pd.DataFrame(new_zones)
                new_zones_df.to_csv(zones_file, mode='a', header=False, index=False)
                logger.info(f"[{tf_name}] ✅ Added {len(new_zones)} new zones to {zones_file}")
            else:
                logger.info(f"[{tf_name}] No new zones to add (all zones already exist)")
        else:
            zones_df.to_csv(zones_file, index=False)
            logger.info(f"[{tf_name}] ✅ Created zones file with {len(zones_df)} zones: {zones_file}")
    else:
        # Create new file with headers
        zones_df.to_csv(zones_file, index=False)
        logger.info(f"[{tf_name}] ✅ Created new zones file with {len(zones_df)} zones: {zones_file}")

def add_wma_crossover_columns(df, analysis_result):
    """Add WMA5_10 column only (no zones)"""

    # Initialize WMA5_10 column
    df['wma5_10'] = 'HOLD'

    if analysis_result is None or len(df) < 10:
        return df

    # Add WMA values and signals
    wma5 = analysis_result['wma5']
    wma10 = analysis_result['wma10']

    # Add WMA5_10 column based on crossover signals
    for i in range(len(df)):
        if i < len(wma5) and i < len(wma10):
            if not np.isnan(wma5[i]) and not np.isnan(wma10[i]):
                if wma5[i] > wma10[i]:
                    df.iloc[i, df.columns.get_loc('wma5_10')] = 'UP'
                elif wma5[i] < wma10[i]:
                    df.iloc[i, df.columns.get_loc('wma5_10')] = 'DOWN'
                else:
                    df.iloc[i, df.columns.get_loc('wma5_10')] = 'HOLD'

    return df

def add_wma_and_zone_columns(df, analysis_result):
    """Add WMA5_10 and PriceActive columns to DataFrame"""

    # Initialize columns
    df['wma5_10'] = 'HOLD'
    df['PriceActive'] = ''

    if analysis_result is None or len(df) < 10:
        return df

    # Add WMA values and signals
    wma5 = analysis_result['wma5']
    wma10 = analysis_result['wma10']

    # Add WMA5_10 column based on crossover signals
    for i in range(len(df)):
        if i < len(wma5) and i < len(wma10):
            if not np.isnan(wma5[i]) and not np.isnan(wma10[i]):
                if wma5[i] > wma10[i]:
                    df.iloc[i, df.columns.get_loc('wma5_10')] = 'UP'
                elif wma5[i] < wma10[i]:
                    df.iloc[i, df.columns.get_loc('wma5_10')] = 'DOWN'
                else:
                    df.iloc[i, df.columns.get_loc('wma5_10')] = 'HOLD'

    # Add PriceActive column based on zones - only mark the specific zone level candle
    if analysis_result.get('zones'):
        for zone in analysis_result['zones']:
            start_time = pd.to_datetime(zone['start_timestamp'])
            end_time = pd.to_datetime(zone['end_timestamp'])
            zone_type = zone['zone_type']

            # Find candles within this zone timeframe
            zone_mask = (df['timestamp'] >= start_time) & (df['timestamp'] <= end_time)
            zone_candles = df[zone_mask]

            if len(zone_candles) > 0:
                if zone_type == 'SUPPLY':
                    # Find the candle with the highest high (supply level)
                    max_high_idx = zone_candles['high'].idxmax()
                    df.loc[max_high_idx, 'PriceActive'] = 'SUPPLY'
                elif zone_type == 'DEMAND':
                    # Find the candle with the lowest low (demand level)
                    min_low_idx = zone_candles['low'].idxmin()
                    df.loc[min_low_idx, 'PriceActive'] = 'DEMAND'

    return df

def add_enhanced_wma_and_zone_columns(df, analysis_result):
    """
    🔥 ENHANCED COLUMN ADDITION FOR COMPREHENSIVE SUPPLY/DEMAND ANALYSIS

    Adds comprehensive columns to match the reference CSV format:
    - WMA5 and WMA10 actual values
    - WMA5_10 crossover signals (UP/DOWN/HOLD)
    - Supply/Demand zone markers
    - Zone levels and strength indicators
    - PriceActive markers for specific zone levels

    This creates output compatible with candle_data_with_price_action.csv format
    """

    if analysis_result is None or len(df) < 10:
        return df

    # Get WMA arrays
    wma5 = analysis_result['wma5']
    wma10 = analysis_result['wma10']

    # 📊 STEP 1: Add WMA values and crossover signals
    for i in range(len(df)):
        if i < len(wma5) and i < len(wma10):
            # Add actual WMA values
            if not np.isnan(wma5[i]):
                df.iloc[i, df.columns.get_loc('wma5')] = round(wma5[i], 2)
            if not np.isnan(wma10[i]):
                df.iloc[i, df.columns.get_loc('wma10')] = round(wma10[i], 2)

            # Add crossover signals
            if not np.isnan(wma5[i]) and not np.isnan(wma10[i]):
                if wma5[i] > wma10[i]:
                    df.iloc[i, df.columns.get_loc('wma5_10')] = 'UP'
                elif wma5[i] < wma10[i]:
                    df.iloc[i, df.columns.get_loc('wma5_10')] = 'DOWN'
                else:
                    df.iloc[i, df.columns.get_loc('wma5_10')] = 'HOLD'

    # 🎯 STEP 2: Add supply/demand zone information
    if analysis_result.get('zones'):
        for zone in analysis_result['zones']:
            start_time = pd.to_datetime(zone['start_timestamp'])
            end_time = pd.to_datetime(zone['end_timestamp'])
            zone_type = zone['zone_type']
            zone_level = zone['zone_level']
            zone_strength = zone['zone_strength']

            # Find candles within this zone timeframe
            zone_mask = (df['timestamp'] >= start_time) & (df['timestamp'] <= end_time)
            zone_indices = df[zone_mask].index

            if len(zone_indices) > 0:
                # Mark all candles in the zone range
                for idx in zone_indices:
                    if zone_type == 'SUPPLY':
                        df.loc[idx, 'supply_zone'] = f"SUPPLY_{zone_strength}"
                        df.loc[idx, 'zone_level'] = round(zone_level, 2)
                        df.loc[idx, 'zone_strength'] = zone_strength
                    elif zone_type == 'DEMAND':
                        df.loc[idx, 'demand_zone'] = f"DEMAND_{zone_strength}"
                        df.loc[idx, 'zone_level'] = round(zone_level, 2)
                        df.loc[idx, 'zone_strength'] = zone_strength

                # Mark the specific zone level candle in PriceActive
                if zone_type == 'SUPPLY':
                    # Find the candle with the highest high (supply level)
                    zone_candles = df.loc[zone_indices]
                    max_high_idx = zone_candles['high'].idxmax()
                    df.loc[max_high_idx, 'PriceActive'] = 'SUPPLY'
                elif zone_type == 'DEMAND':
                    # Find the candle with the lowest low (demand level)
                    zone_candles = df.loc[zone_indices]
                    min_low_idx = zone_candles['low'].idxmin()
                    df.loc[min_low_idx, 'PriceActive'] = 'DEMAND'

    return df

def update_historical_csv(timeframe):
    """Update historical CSV file with current deque data"""
    tf_name = TIMEFRAMES[timeframe]['name']
    historical_file = get_output_filename(timeframe, 'historical')

    try:
        # Convert deque to DataFrame
        if historical_data_storage[timeframe]:
            df = pd.DataFrame(list(historical_data_storage[timeframe]))
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df = df.sort_values('timestamp')

            # Save to historical CSV file
            df.to_csv(historical_file, index=False)
            logger.debug(f"[{tf_name}] Updated historical CSV with {len(df)} candles")

    except Exception as e:
        logger.error(f"[{tf_name}] Error updating historical CSV: {e}")

def timeframe_worker(timeframe):
    """Worker function for each timeframe - runs in separate thread"""
    tf_name = TIMEFRAMES[timeframe]['name']
    logger.info(f"[{tf_name}] Starting timeframe worker...")

    # Initialize CSV files and check for existing data
    has_historical_data, has_live_data = initialize_csv_files(timeframe)

    # Determine what data needs to be fetched
    historical_data_fetched = has_historical_data
    live_data_fetched = has_live_data

    while True:
        try:
            # Check if market is open
            if not is_market_open():
                current_time = get_current_time_ist()
                logger.info(f"[{tf_name}] Market is closed at {current_time.strftime('%Y-%m-%d %H:%M:%S')} IST")

                # If market is closed and we don't have comprehensive historical data, fetch it
                if not historical_data_fetched:
                    logger.info(f"[{tf_name}] 🔄 Market closed - fetching comprehensive historical data...")
                    success = fetch_comprehensive_historical_data(timeframe)
                    if success:
                        historical_data_fetched = True
                        logger.info(f"[{tf_name}] ✅ Comprehensive historical data fetch completed")
                    else:
                        logger.warning(f"[{tf_name}] Failed to fetch comprehensive historical data")

                # If we don't have today's live data, fetch it
                if not live_data_fetched:
                    logger.info(f"[{tf_name}] 🔄 Fetching today's data for live file...")
                    success = fetch_and_process_today_data(timeframe)
                    if success:
                        live_data_fetched = True
                        logger.info(f"[{tf_name}] ✅ Today's data fetch completed")

                # Wait before checking again
                time.sleep(60)  # Check every minute
                continue

            # Fetch comprehensive historical data once when market is open (if not already done)
            if not historical_data_fetched:
                logger.info(f"[{tf_name}] 🔄 First time market is open - fetching comprehensive historical data...")
                success = fetch_comprehensive_historical_data(timeframe)
                if success:
                    historical_data_fetched = True
                    logger.info(f"[{tf_name}] ✅ Comprehensive historical data fetch completed")
                else:
                    logger.warning(f"[{tf_name}] Failed to fetch comprehensive historical data, will retry...")
                    time.sleep(30)
                    continue

            # Fetch today's data if not already done
            if not live_data_fetched:
                logger.info(f"[{tf_name}] 🔄 Fetching today's data for live file...")
                success = fetch_and_process_today_data(timeframe)
                if success:
                    live_data_fetched = True
                    logger.info(f"[{tf_name}] ✅ Today's data fetch completed. Starting live collection...")
                else:
                    logger.warning(f"[{tf_name}] Failed to fetch today's data, will retry...")
                    time.sleep(30)
                    continue

            # Get current time
            current_time = get_current_time_ist()

            # Check if we should fetch a candle for this timeframe
            if should_fetch_candle(timeframe, current_time):
                # Wait for buffer time to ensure candle is complete
                buffer_seconds = TIMEFRAMES[timeframe]['buffer_seconds']
                logger.info(f"[{tf_name}] Candle boundary reached, waiting {buffer_seconds}s buffer...")
                time.sleep(buffer_seconds)

                # Get the target candle time
                target_time = get_target_candle_time(timeframe, current_time)

                # Try to find and save the candle
                success = find_and_save_candle(timeframe, target_time)

                if not success:
                    logger.warning(f"[{tf_name}] Failed to fetch candle data")

            # Wait based on timeframe interval
            wait_interval = TIMEFRAMES[timeframe]['wait_interval']
            time.sleep(wait_interval)

        except KeyboardInterrupt:
            logger.info(f"[{tf_name}] Received keyboard interrupt, stopping...")
            break
        except Exception as e:
            logger.error(f"[{tf_name}] Unexpected error: {str(e)}")
            logger.info(f"[{tf_name}] Retrying in {RETRY_DELAY * 5} seconds...")
            time.sleep(RETRY_DELAY * 5)

def main():
    """
    🚀 MAIN MULTI-TIMEFRAME LIVE DATA FETCHER WITH CROSSOVER DETECTION

    This is the main entry point that:

    📊 HISTORICAL DATA MANAGEMENT:
    - Fetches comprehensive historical data for all timeframes
    - Maintains rolling windows (1min=3days, 5min=7days, 60min=30days)
    - Analyzes WMA crossover signals in historical data

    🔥 LIVE DATA PROCESSING:
    - Monitors market hours and fetches live candles
    - Performs real-time WMA crossover detection (UP/DOWN signals)
    - Updates CSV files with WMA5_10 column
    - Logs crossover alerts for trading decisions

    🎯 KEY FEATURES:
    - Multi-timeframe parallel processing (1min, 5min, 60min)
    - Real-time crossover signal detection and alerts
    - Enhanced CSV files with WMA5_10 indicators
    - Clean crossover-only analysis (no zones)
    """
    logger.info("🚀 Starting Multi-Timeframe Live Data Fetcher...")
    logger.info(f"📊 Timeframes: {', '.join([TIMEFRAMES[tf]['name'] for tf in TIMEFRAMES.keys()])}")
    logger.info(f"📈 Symbol: {SYMBOL} ({EXCHANGE})")
    logger.info(f"⏰ Market Hours: {MARKET_OPEN_TIME} - {MARKET_CLOSE_TIME} IST")

    # Create thread pool for parallel execution
    with ThreadPoolExecutor(max_workers=len(TIMEFRAMES), thread_name_prefix="TF") as executor:
        # Submit each timeframe worker to the thread pool
        futures = []
        for timeframe in TIMEFRAMES.keys():
            future = executor.submit(timeframe_worker, timeframe)
            futures.append(future)
            logger.info(f"✅ Started worker for {TIMEFRAMES[timeframe]['name']}")

        try:
            # Wait for all workers to complete (they run indefinitely)
            for future in futures:
                future.result()
        except KeyboardInterrupt:
            logger.info("🛑 Received keyboard interrupt, shutting down all workers...")
            executor.shutdown(wait=False)
        except Exception as e:
            logger.error(f"❌ Fatal error in main: {str(e)}")
        finally:
            logger.info("🔚 Multi-timeframe data collection ended")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        logger.info("Data collection stopped by user")
    except Exception as e:
        logger.error(f"Fatal error: {str(e)}")
    finally:
        logger.info("Application terminated")
