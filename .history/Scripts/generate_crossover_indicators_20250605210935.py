#!/usr/bin/env python3
"""
🔥 ENHANCED SUPPLY/DEMAND ZONE AND CROSSOVER INDICATOR GENERATOR

This script processes historical data to generate comprehensive supply/demand analysis:
- 5-period and 10-period Weighted Moving Averages (WMA)
- WMA crossover detection (UP/DOWN signals)
- Supply/Demand zone identification between crossovers
- Enhanced CSV output with zone levels and metadata

Processes all timeframes: 1min, 5min, 60min
Output format matches: candle_data_with_price_action.csv
"""

import pandas as pd
import numpy as np
import os
import sys

# Import the enhanced functions from the main script
sys.path.append('.')
from multi_timeframe_live_fetcher import (
    calculate_wma_signals,  # Enhanced function with zone detection
    add_enhanced_wma_and_zone_columns,  # Enhanced column addition
    append_zones_to_csv,  # Zone persistence
    TIMEFRAMES
)

def process_timeframe_supply_demand_zones(timeframe):
    """
    🔥 ENHANCED PROCESSING FOR SUPPLY/DEMAND ZONES AND CROSSOVERS

    This function processes historical data to:
    1. Calculate 5-period and 10-period WMAs
    2. Detect crossover signals (UP/DOWN)
    3. Create supply/demand zones between crossovers
    4. Generate comprehensive CSV output with zone data
    5. Save zones to dedicated CSV files
    """
    tf_name = TIMEFRAMES[timeframe]['name']
    historical_file = f'Historical_Data_Layer/historical_{tf_name}.csv'

    print(f"\n🔥 Processing {tf_name} timeframe for supply/demand zones...")

    # Check if historical file exists
    if not os.path.exists(historical_file):
        print(f"❌ Historical file not found: {historical_file}")
        return False

    # Read historical data
    df = pd.read_csv(historical_file)
    df['timestamp'] = pd.to_datetime(df['timestamp'])

    print(f"📊 Loaded {len(df)} candles from {historical_file}")

    if len(df) < 10:
        print(f"⚠️ Not enough data for analysis (need at least 10 candles)")
        return False

    # Convert to list format for analysis
    data_list = [row.to_dict() for _, row in df.iterrows()]

    # 📈 STEP 1: Perform comprehensive WMA and zone analysis
    print(f"🔍 Analyzing WMA crossovers and supply/demand zones...")
    analysis_result = calculate_wma_signals(data_list)

    if not analysis_result:
        print(f"❌ Analysis failed - insufficient data")
        return False

    # 📊 STEP 2: Report crossover signals
    if analysis_result['signals']:
        print(f"📊 Found {len(analysis_result['signals'])} WMA crossover signals")

        # Log signal summary
        up_signals = [s for s in analysis_result['signals'] if s['signal'] == 'UP']
        down_signals = [s for s in analysis_result['signals'] if s['signal'] == 'DOWN']

        print(f"🔼 UP signals: {len(up_signals)}, 🔽 DOWN signals: {len(down_signals)}")
    else:
        print(f"ℹ️ No WMA crossover signals detected")

    # 🎯 STEP 3: Report supply/demand zones
    if analysis_result['zones']:
        print(f"🎯 Created {len(analysis_result['zones'])} supply/demand zones")

        # Log zone summary
        supply_zones = [z for z in analysis_result['zones'] if z['zone_type'] == 'SUPPLY']
        demand_zones = [z for z in analysis_result['zones'] if z['zone_type'] == 'DEMAND']

        print(f"📈 SUPPLY zones: {len(supply_zones)}, 📉 DEMAND zones: {len(demand_zones)}")

        # Save zones to dedicated CSV file
        append_zones_to_csv(timeframe, analysis_result['zones'])
        print(f"💾 Zones saved to Supply_Demand_Zones/zones_{tf_name}.csv")
    else:
        print(f"ℹ️ No supply/demand zones created")

    # 📊 STEP 4: Add comprehensive columns to historical data
    print(f"📈 Adding enhanced columns (WMA values, crossovers, zones)...")

    # Add required columns for enhanced output
    required_columns = {
        'wma5': 0.0,
        'wma10': 0.0,
        'wma5_10': 'HOLD',
        'supply_zone': '',
        'demand_zone': '',
        'zone_level': 0.0,
        'zone_strength': '',
        'PriceActive': ''
    }

    for col, default_val in required_columns.items():
        if col not in df.columns:
            df[col] = default_val

    # Apply enhanced analysis to DataFrame
    enhanced_df = add_enhanced_wma_and_zone_columns(df.copy(), analysis_result)

    # Save enhanced historical CSV
    enhanced_df.to_csv(historical_file, index=False)
    print(f"✅ Enhanced historical CSV saved: {historical_file}")

    # 📋 STEP 5: Display sample results
    print(f"\n📋 Sample enhanced data (first 3 rows with zones):")
    zone_samples = enhanced_df[enhanced_df['PriceActive'] != ''].head(3)
    if not zone_samples.empty:
        for _, row in zone_samples.iterrows():
            print(f"   {row['timestamp'].strftime('%Y-%m-%d %H:%M')} | "
                  f"{row['PriceActive']} | Level: {row['zone_level']:.2f} | "
                  f"WMA5: {row['wma5']:.2f} | WMA10: {row['wma10']:.2f}")
    else:
        print("   No zone markers found in data")

    return True

def main():
    """
    🚀 MAIN FUNCTION FOR COMPREHENSIVE SUPPLY/DEMAND ZONE ANALYSIS

    Processes all timeframes (1min, 5min, 60min) to:
    1. Calculate WMA crossover signals
    2. Create supply/demand zones between crossovers
    3. Generate enhanced CSV files with zone data
    4. Save zones to dedicated CSV files

    Output format matches: candle_data_with_price_action.csv
    """
    print("🚀 Starting Enhanced Supply/Demand Zone Analysis...")
    print("📊 Processing timeframes: 1min, 5min, 60min")
    print("🎯 Creating supply/demand zones between WMA crossovers")

    # Process each timeframe
    total_zones = 0
    total_signals = 0

    for timeframe in TIMEFRAMES.keys():
        try:
            print(f"\n{'='*60}")
            success = process_timeframe_supply_demand_zones(timeframe)
            if success:
                print(f"✅ Successfully processed {TIMEFRAMES[timeframe]['name']} timeframe")

                # Try to count zones created
                try:
                    zones_file = f"Supply_Demand_Zones/zones_{TIMEFRAMES[timeframe]['name']}.csv"
                    if os.path.exists(zones_file):
                        zones_df = pd.read_csv(zones_file)
                        total_zones += len(zones_df)
                        print(f"📊 Total zones in {TIMEFRAMES[timeframe]['name']}: {len(zones_df)}")
                except:
                    pass

            else:
                print(f"❌ Failed to process {TIMEFRAMES[timeframe]['name']} timeframe")
        except Exception as e:
            print(f"❌ Error processing {TIMEFRAMES[timeframe]['name']}: {str(e)}")

    print(f"\n{'='*60}")
    print("🎉 Enhanced Supply/Demand Zone Analysis Completed!")
    print(f"\n📊 SUMMARY:")
    print(f"   📈 Total supply/demand zones created: {total_zones}")
    print(f"   📁 Zone files saved to: Supply_Demand_Zones/")
    print(f"   📋 Enhanced CSV files updated in: Historical_Data_Layer/")

    print(f"\n📊 Enhanced CSV files now contain:")
    print(f"   - wma5, wma10: Actual WMA values")
    print(f"   - wma5_10: UP/DOWN/HOLD crossover signals")
    print(f"   - supply_zone, demand_zone: Zone markers")
    print(f"   - zone_level: Exact supply/demand price levels")
    print(f"   - zone_strength: HIGH/MEDIUM/LOW zone strength")
    print(f"   - PriceActive: SUPPLY/DEMAND markers at zone levels")

    print(f"\n🎯 Zone files contain comprehensive zone metadata:")
    print(f"   - Zone type, level, and strength")
    print(f"   - Start/end timestamps and crossover signals")
    print(f"   - OHLCV data for zone creation candles")
    print(f"   - WMA values at zone boundaries")

    print(f"\n✨ Ready for live trading analysis!")
    print(f"   Use multi_timeframe_live_fetcher.py for real-time zone detection")

if __name__ == "__main__":
    main()
