# Multi-Timeframe Live Data Fetcher with WMA Crossover Detection - Requirements
# ============================================================================

# Core DhanHQ API Library
dhanhq>=

# Data Processing and Analysis
pandas>=2.0.0
numpy>=1.24.0

# Built-in modules (no installation required)
# datetime - built-in (includes timezone support)
# time - built-in
# os - built-in
# threading - built-in
# collections - built-in (deque)
# logging - built-in

# Optional: Enhanced Data Analysis (if you want to extend functionality)
# matplotlib>=3.7.0
# seaborn>=0.12.0
# plotly>=5.15.0

# Optional: Advanced Data Processing (if you want to extend functionality)
# scipy>=1.10.0
# scikit-learn>=1.3.0

# Optional: Database Support (if you want to store data in databases)
# sqlalchemy>=2.0.0
# pymongo>=4.4.0
# psycopg2-binary>=2.9.0

# Optional: Web Interface (if you want to create a web dashboard)
# flask>=2.3.0
# streamlit>=1.25.0
# dash>=2.11.0

# Optional: Configuration Management
# python-dotenv>=1.0.0
# configparser - built-in

# Optional: Logging and Monitoring
# loguru>=0.7.0

# Optional: Testing (for development)
# pytest>=7.4.0
# pytest-cov>=4.1.0

# Optional: Code Quality (for development)
# black>=23.7.0
# flake8>=6.0.0
# mypy>=1.5.0

# Python Version Requirement
# python>=3.8

# Installation Instructions:
# ========================
#
# Option 1: Using uv (recommended)
# curl -LsSf https://astral.sh/uv/install.sh | sh
# uv pip install -r requirements.txt
#
# Option 2: Using pip
# python -m venv .venv
# source .venv/bin/activate  # On Windows: .venv\Scripts\activate
# pip install -r requirements.txt
#
# Option 3: Minimal installation
# pip install dhanhq pandas numpy
