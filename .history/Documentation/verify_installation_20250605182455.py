#!/usr/bin/env python3
"""
Installation Verification Script for Multi-Timeframe Live Data Fetcher
"""

import sys
import os

def check_python_version():
    """Check if Python version is compatible"""
    print("🐍 Checking Python version...")
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print(f"   ✅ Python {version.major}.{version.minor}.{version.micro} - Compatible")
        return True
    else:
        print(f"   ❌ Python {version.major}.{version.minor}.{version.micro} - Requires Python 3.8+")
        return False

def check_dependencies():
    """Check if required dependencies are installed"""
    print("\n📦 Checking dependencies...")
    
    dependencies = {
        'dhanhq': 'DhanHQ API Library',
        'pandas': 'Data Processing Library', 
        'numpy': 'Numerical Computing Library'
    }
    
    all_installed = True
    
    for package, description in dependencies.items():
        try:
            __import__(package)
            print(f"   ✅ {package} - {description}")
        except ImportError:
            print(f"   ❌ {package} - {description} (NOT INSTALLED)")
            all_installed = False
    
    return all_installed

def check_files():
    """Check if required files exist"""
    print("\n📁 Checking required files...")
    
    required_files = {
        'multi_timeframe_live_fetcher.py': 'Main script',
        'generate_crossover_indicators.py': 'Utility script',
        'requirements.txt': 'Dependencies list',
        'requirements-minimal.txt': 'Minimal dependencies',
        'README.md': 'Documentation'
    }
    
    all_present = True
    
    for filename, description in required_files.items():
        if os.path.exists(filename):
            print(f"   ✅ {filename} - {description}")
        else:
            print(f"   ❌ {filename} - {description} (NOT FOUND)")
            all_present = False
    
    return all_present

def check_folders():
    """Check if data folders exist or can be created"""
    print("\n📂 Checking data folders...")
    
    folders = [
        'Historical_Data_Layer',
        'Live_Data_Layer'
    ]
    
    for folder in folders:
        if os.path.exists(folder):
            print(f"   ✅ {folder}/ - Exists")
        else:
            try:
                os.makedirs(folder, exist_ok=True)
                print(f"   ✅ {folder}/ - Created")
            except Exception as e:
                print(f"   ❌ {folder}/ - Cannot create ({e})")
                return False
    
    return True

def test_imports():
    """Test importing main functions"""
    print("\n🧪 Testing imports...")
    
    try:
        from multi_timeframe_live_fetcher import (
            calculate_wma_crossover_signals,
            add_wma_crossover_columns,
            TIMEFRAMES
        )
        print("   ✅ Main functions import successfully")
        print(f"   ✅ Configured timeframes: {list(TIMEFRAMES.keys())}")
        return True
    except Exception as e:
        print(f"   ❌ Import error: {e}")
        return False

def main():
    """Main verification function"""
    print("🎯 Multi-Timeframe Live Data Fetcher - Installation Verification")
    print("=" * 70)
    
    checks = [
        ("Python Version", check_python_version),
        ("Dependencies", check_dependencies),
        ("Required Files", check_files),
        ("Data Folders", check_folders),
        ("Import Test", test_imports)
    ]
    
    all_passed = True
    
    for check_name, check_func in checks:
        result = check_func()
        if not result:
            all_passed = False
    
    print("\n" + "=" * 70)
    
    if all_passed:
        print("🎉 ALL CHECKS PASSED! Installation is ready.")
        print("\n🚀 You can now run:")
        print("   uv run python multi_timeframe_live_fetcher.py")
        print("   or")
        print("   python multi_timeframe_live_fetcher.py")
    else:
        print("❌ SOME CHECKS FAILED! Please fix the issues above.")
        print("\n📋 Installation steps:")
        print("   1. Install dependencies: uv pip install -r requirements-minimal.txt")
        print("   2. Configure API credentials in multi_timeframe_live_fetcher.py")
        print("   3. Run this verification script again")
    
    print("\n📖 For detailed setup instructions, see:")
    print("   - README.md")
    print("   - SETUP_GUIDE.md")

if __name__ == "__main__":
    main()
