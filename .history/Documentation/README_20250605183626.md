# 🎯 Multi-Timeframe Live Data Fetcher with WMA Crossover Detection

A powerful Python application for **real-time multi-timeframe data collection** with **WMA crossover signal detection** for NIFTY INDEX trading.

## 🚀 **Key Features**

- **📊 Multi-Timeframe Processing**: Simultaneous 1min, 5min, and 60min data collection
- **🔥 Real-time WMA Crossover Detection**: Live UP/DOWN signal alerts
- **📈 Enhanced CSV Output**: Historical and live data with WMA5_10 indicators
- **⚡ Parallel Processing**: Efficient multi-threaded data collection
- **🎯 Clean Signal Focus**: Pure crossover analysis without zone complexity
- **📅 Historical Data Management**: Automatic rolling window maintenance

## 📋 **Prerequisites**

### **System Requirements**
- **Python 3.8+** (recommended: Python 3.9+)
- **uv package manager** (recommended) or pip
- **DhanHQ API access** with valid credentials

### **API Setup**
- Active DhanHQ trading account
- Valid Client ID and Access Token
- API access enabled

## 🔧 **Installation**

### **Step 1: Clone Repository**
```bash
git clone <repository-url>
cd multi-timeframe-live-fetcher
```

### **Step 2: Install Dependencies**

#### **Option A: Using uv (Recommended)**
```bash
# Install uv if not already installed
curl -LsSf https://astral.sh/uv/install.sh | sh

# Install dependencies
uv pip install -r Config/requirements.txt
```

#### **Option B: Using pip**
```bash
# Create virtual environment
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### **Step 3: Configure API Credentials**
Edit `Scripts/multi_timeframe_live_fetcher.py` and update:
```python
CLIENT_ID = "YOUR_CLIENT_ID"
ACCESS_TOKEN = "YOUR_ACCESS_TOKEN"
```

## 🚀 **Usage**

### **Run Live Data Fetcher**
```bash
# Using uv (recommended)
uv run python Scripts/multi_timeframe_live_fetcher.py

# Using standard python
python Scripts/multi_timeframe_live_fetcher.py
```

### **Generate Crossover Indicators for Existing Data**
```bash
# Process existing historical data
python Scripts/generate_crossover_indicators.py
```

## 📊 **Output Structure**

### **Folder Organization**
```
Current Directory (live-data-fetching)/
├── 📊 Data Folders
│   ├── Historical_Data_Layer/
│   │   ├── historical_1min.csv      # 3 days rolling window
│   │   ├── historical_5min.csv      # 7 days rolling window
│   │   └── historical_60min.csv     # 30 days rolling window
│   ├── Live_Data_Layer/
│   │   ├── NIFTY_INDEX_1min_live.csv
│   │   ├── NIFTY_INDEX_5min_live.csv
│   │   └── NIFTY_INDEX_60min_live.csv
│   ├── Dependencies/, recentdata/, stock_data/, logs/
├── 🎯 Scripts/
│   ├── multi_timeframe_live_fetcher.py    # Main crossover detection script
│   ├── generate_crossover_indicators.py   # Utility for existing data
│   └── Other script files
├── 📖 Documentation/
│   ├── README.md, SETUP_GUIDE.md, etc.
├── 📦 Config/
│   ├── requirements.txt, dhan_nse_*.csv
└── 🔧 Other_Files/
    ├── ta-lib/, __pycache__/, *.log
```

### **CSV Data Format**
```csv
open,high,low,close,volume,timestamp,wma5_10
24662.4,24705.5,24655.8,24705.5,0.0,2025-06-03 10:15:00,UP
24645.25,24650.35,24612.1,24642.85,0.0,2025-06-03 10:40:00,DOWN
24630.4,24640.25,24619.15,24639.5,0.0,2025-06-03 10:00:00,HOLD
```

## 🎯 **WMA Crossover Signals**

### **Signal Types**
- **UP**: 5 WMA crosses above 10 WMA (Bullish signal)
- **DOWN**: 5 WMA crosses below 10 WMA (Bearish signal)  
- **HOLD**: No clear crossover (Neutral state)

### **Real-time Alerts**
```
[1min] 🚨 NEW LIVE CROSSOVER: UP at 2025-06-05 14:31:00
[1min] 📊 WMA5: 24755.23, WMA10: 24752.45
[5min] 🚨 NEW LIVE CROSSOVER: DOWN at 2025-06-05 14:35:00
[5min] 📊 WMA5: 24748.12, WMA10: 24751.67
```

## ⚙️ **Configuration**

### **Timeframe Settings**
```python
TIMEFRAMES = {
    '1': {
        'name': '1min',
        'historical_days': 3,    # 3 days retention
        'max_candles': 1125      # 3 days * 375 candles/day
    },
    '5': {
        'name': '5min', 
        'historical_days': 7,    # 7 days retention
        'max_candles': 525       # 7 days * 75 candles/day
    },
    '60': {
        'name': '60min',
        'historical_days': 30,   # 30 days retention
        'max_candles': 180       # 30 days * 6 candles/day
    }
}
```

### **Market Hours**
- **Market Open**: 9:15 AM IST
- **Market Close**: 3:30 PM IST
- **Trading Days**: Monday to Friday (excluding holidays)

## 🔍 **Monitoring & Logs**

### **Log Levels**
- **INFO**: Crossover signals and system status
- **DEBUG**: Detailed processing information
- **ERROR**: API errors and system issues

### **Real-time Monitoring**
```bash
# View live logs
tail -f multi_timeframe_fetcher.log

# Check data generation
ls -la Historical_Data_Layer/
ls -la Live_Data_Layer/
```

## 🛠️ **Troubleshooting**

### **Common Issues**

#### **1. Import Errors**
```bash
# Install missing dependencies
uv pip install pandas numpy dhanhq
```

#### **2. API Authentication**
- Verify CLIENT_ID and ACCESS_TOKEN
- Check DhanHQ API access status
- Ensure tokens haven't expired

#### **3. No Data Generated**
- Confirm market hours (9:15 AM - 3:30 PM IST)
- Check internet connectivity
- Verify trading day (not weekend/holiday)

#### **4. Memory Issues**
- Monitor deque sizes in logs
- Check available system memory
- Restart script if needed

### **Debug Mode**
Enable detailed logging by setting:
```python
logging.basicConfig(level=logging.DEBUG)
```

## 📈 **Performance Metrics**

### **Data Collection Rates**
- **1min**: ~375 candles per trading day
- **5min**: ~75 candles per trading day  
- **60min**: ~6 candles per trading day

### **Memory Usage**
- **1min**: ~1125 candles in memory (3 days)
- **5min**: ~525 candles in memory (7 days)
- **60min**: ~180 candles in memory (30 days)

## 🔄 **Maintenance**

### **Update Dependencies**
```bash
# Using uv
uv pip install --upgrade pandas numpy dhanhq

# Using pip
pip install --upgrade -r requirements.txt
```

### **Data Cleanup**
```bash
# Clear old data (optional)
rm -rf Historical_Data_Layer/*.csv
rm -rf Live_Data_Layer/*.csv
```

## 📞 **Support**

### **Resources**
- **DhanHQ API Docs**: https://dhanhq.co/docs/
- **Pandas Documentation**: https://pandas.pydata.org/docs/
- **Python Documentation**: https://docs.python.org/3/

### **Useful Commands**
```bash
# Check Python version
python --version

# List installed packages  
uv pip list

# Check script status
ps aux | grep multi_timeframe_live_fetcher

# Stop background process
pkill -f multi_timeframe_live_fetcher
```

## ✅ **Success Checklist**

- [ ] Python 3.8+ installed
- [ ] uv or pip package manager available
- [ ] Dependencies installed successfully
- [ ] DhanHQ credentials configured
- [ ] Script runs without errors
- [ ] Historical data files generated
- [ ] Live data collection working
- [ ] WMA crossover signals detected
- [ ] Logs showing proper operation

## 🎉 **Ready for Trading!**

Your **Multi-Timeframe Live Data Fetcher with WMA Crossover Detection** is now ready for real-time trading signal analysis!

**Happy Trading! 📈🎯**
