# Multi-Timeframe Live Data Fetcher with WMA Crossover Detection - Minimal Requirements
# ===================================================================================
# Only the essential packages required to run the crossover detection system

# Core DhanHQ API Library
dhanhq>=2.2.0

# Data Processing and Analysis
pandas>=2.0.0
numpy>=1.24.0

# Note: The following are built-in Python modules (no installation required):
# - os
# - datetime
# - time
# - threading
# - collections (deque)
# - logging

# Installation Instructions:
# ========================
#
# Option 1: Using uv (recommended)
# curl -LsSf https://astral.sh/uv/install.sh | sh
# uv pip install -r requirements-minimal.txt
#
# Option 2: Using pip
# pip install dhanhq>=2.2.0 pandas>=2.0.0 numpy>=1.24.0
#
# Option 3: Latest versions
# pip install dhanhq pandas numpy

# Verification:
# ============
# python -c "import dhanhq, pandas, numpy; print('✅ All dependencies installed successfully')"
