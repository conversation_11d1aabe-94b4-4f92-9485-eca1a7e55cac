#!/usr/bin/env python3
"""
Enhanced DhanHQ Live Data Fetcher
- Continuous operation waiting for market hours
- 5 trading days of historical data management
- Live data collection during market hours
- Comprehensive timeframe support (1min, 5min, 15min, 25min, 60min, daily)
- Rolling window data management with 3-day daily data retention
"""

import os
import pandas as pd
import datetime
import time
from dhanhq import dhanhq

# ============================================================================
# CONFIGURATION
# ============================================================================

# API Configuration
CLIENT_ID = "1105577608"
ACCESS_TOKEN = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzUwOTU2MDc2LCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTEwNTU3NzYwOCJ9.suPPlPFFhOK_W4AumsLqIGMhF3Ez_rrFT4KF90Ndj3UruoRmOJ1AonS8BtFpYjWf4rP243mLO5HlWZqqn3XHDw"

# Data Management Configuration
HISTORICAL_DAYS = 5  # Keep last 5 trading days of intraday data
DAILY_DATA_DAYS = 3  # Keep last 3 days of daily data (rolling window)
LIVE_DATA_INTERVAL = 30  # Fetch live quotes every 30 seconds
INTRADAY_UPDATE_INTERVAL = 60  # Update intraday data every 60 seconds

# Market Timing
MARKET_OPEN = datetime.time(9, 15)
MARKET_CLOSE = datetime.time(15, 30)
PRE_MARKET_START = datetime.time(9, 0)

# API Configuration - Optimized for DhanHQ v2.2 (no rate limits on minute timeframes)
API_RATE_LIMIT_DELAY = 0.05
MAX_RETRIES = 2
RETRY_DELAY = 0.5

# Stock Configuration - 5 different stocks + NIFTY 50
STOCKS = [
    {
        'name': 'RELIANCE', 
        'security_id': '2885', 
        'exchange_segment': 'NSE_EQ', 
        'instrument_type': 'EQUITY'
    },
    {
        'name': 'TCS', 
        'security_id': '11536', 
        'exchange_segment': 'NSE_EQ', 
        'instrument_type': 'EQUITY'
    },
    {
        'name': 'INFY', 
        'security_id': '1594', 
        'exchange_segment': 'NSE_EQ', 
        'instrument_type': 'EQUITY'
    },
    {
        'name': 'HDFCBANK', 
        'security_id': '1333', 
        'exchange_segment': 'NSE_EQ', 
        'instrument_type': 'EQUITY'
    },
    {
        'name': 'ICICIBANK', 
        'security_id': '4963', 
        'exchange_segment': 'NSE_EQ', 
        'instrument_type': 'EQUITY'
    },
    {
        "name": "NIFTY 50",
        "security_id": "13",
        "exchange_segment": "IDX_I",
        "instrument_type": "INDEX"
    }
]

# Timeframe Configuration
TIMEFRAMES = {
    '1min': 1,
    '5min': 5,
    '15min': 15,
    '25min': 25,
    '60min': 60,
    'daily': 'daily'
}

# Initialize Dhan client
dhan = dhanhq(CLIENT_ID, ACCESS_TOKEN)

# Create directories
os.makedirs('stock_data', exist_ok=True)
os.makedirs('logs', exist_ok=True)

# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

def get_current_time():
    """Get current datetime"""
    return datetime.datetime.now()

def get_today():
    """Get today's date"""
    return datetime.datetime.now().date()

def log_event(stock, timeframe, event_type, message):
    """Log events to CSV file with timestamp"""
    timestamp = get_current_time().strftime('%Y-%m-%d %H:%M:%S')
    log_entry = f"{timestamp},{stock},{timeframe},{event_type},{message}\n"
    
    log_file = 'logs/activity_log.csv'
    if not os.path.exists(log_file):
        with open(log_file, 'w') as f:
            f.write("timestamp,stock,timeframe,event_type,message\n")
    
    with open(log_file, 'a') as f:
        f.write(log_entry)
    
    print(f"[{timestamp}] {stock} {timeframe}: {event_type} - {message}")

def is_trading_day(date):
    """Check if given date is a trading day (Monday to Friday)"""
    return date.weekday() < 5  # Monday=0, Sunday=6

def is_market_open():
    """Check if market is currently open"""
    current_time = get_current_time().time()
    return MARKET_OPEN <= current_time <= MARKET_CLOSE

def get_trading_days(days_back=5):
    """Get list of last N trading days"""
    trading_days = []
    current_date = get_today()
    
    while len(trading_days) < days_back:
        if is_trading_day(current_date):
            trading_days.append(current_date)
        current_date -= datetime.timedelta(days=1)
    
    return sorted(trading_days)

def get_filename(stock, timeframe):
    """Generate filename for stock data"""
    if timeframe == 'daily':
        return f"stock_data/{stock['name']}_daily.csv"
    else:
        return f"stock_data/{stock['name']}_{timeframe}.csv"

def wait_for_market_open():
    """Wait until market opens at 9:15 AM"""
    while True:
        current_time = get_current_time()
        current_date = current_time.date()
        
        # Check if it's a trading day
        if not is_trading_day(current_date):
            # If it's weekend, wait until Monday
            days_until_monday = (7 - current_date.weekday()) % 7
            if days_until_monday == 0:  # It's Sunday
                days_until_monday = 1
            
            next_trading_day = current_date + datetime.timedelta(days=days_until_monday)
            wait_until = datetime.datetime.combine(next_trading_day, MARKET_OPEN)
            
            print(f"📅 Non-trading day. Waiting until {wait_until.strftime('%Y-%m-%d %H:%M:%S')}")
            log_event('SYSTEM', 'wait', 'NON_TRADING_DAY', f'Waiting until {wait_until}')
            
        else:
            # It's a trading day, check if market is open
            if is_market_open():
                print("🟢 Market is OPEN!")
                return True
            
            # Market is closed, wait until it opens
            market_open_today = datetime.datetime.combine(current_date, MARKET_OPEN)
            
            if current_time < market_open_today:
                # Market hasn't opened yet today
                wait_until = market_open_today
                print(f"⏰ Waiting for market to open at {wait_until.strftime('%H:%M:%S')}")
                log_event('SYSTEM', 'wait', 'WAITING_FOR_OPEN', f'Market opens at {MARKET_OPEN}')
            else:
                # Market has closed for today, wait until tomorrow
                next_trading_day = current_date + datetime.timedelta(days=1)
                while not is_trading_day(next_trading_day):
                    next_trading_day += datetime.timedelta(days=1)
                
                wait_until = datetime.datetime.combine(next_trading_day, MARKET_OPEN)
                print(f"🔴 Market closed. Waiting until {wait_until.strftime('%Y-%m-%d %H:%M:%S')}")
                log_event('SYSTEM', 'wait', 'MARKET_CLOSED', f'Waiting until {wait_until}')
        
        # Sleep for 60 seconds before checking again
        time.sleep(60)

# ============================================================================
# API FUNCTIONS
# ============================================================================

def api_call_with_retry(api_func, *args, **kwargs):
    """Generic function to handle API calls with retry logic"""
    for attempt in range(MAX_RETRIES):
        try:
            time.sleep(API_RATE_LIMIT_DELAY)
            result = api_func(*args, **kwargs)
            
            if result.get('status') == 'success':
                return result
            else:
                error_msg = result.get('message', 'No message')
                if attempt < MAX_RETRIES - 1:
                    print(f"API call failed (attempt {attempt + 1}): {error_msg}. Retrying...")
                    time.sleep(RETRY_DELAY)
                else:
                    return result
                    
        except Exception as e:
            if attempt < MAX_RETRIES - 1:
                print(f"Exception in API call (attempt {attempt + 1}): {str(e)}. Retrying...")
                time.sleep(RETRY_DELAY)
            else:
                return {'status': 'failure', 'message': str(e), 'data': {}}
    
    return {'status': 'failure', 'message': 'Max retries exceeded', 'data': {}}

def fetch_live_market_quotes():
    """Fetch live market quotes for all stocks"""
    try:
        # Prepare securities dictionary for Market Quote API
        securities = {}
        for stock in STOCKS:
            exchange = stock['exchange_segment']
            if exchange not in securities:
                securities[exchange] = []
            securities[exchange].append(stock['security_id'])
        
        # Fetch OHLC data (includes LTP)
        data = api_call_with_retry(dhan.ohlc_data, securities)
        
        if data['status'] == 'success':
            return data['data']
        else:
            log_event('MARKET_QUOTE', 'live', 'API_ERROR', data.get('message', 'No message'))
            return {}
            
    except Exception as e:
        log_event('MARKET_QUOTE', 'live', 'EXCEPTION', str(e))
        return {}

def fetch_intraday_data(stock, timeframe, from_dt, to_dt):
    """Fetch intraday data for a specific timeframe"""
    try:
        interval = TIMEFRAMES[timeframe]
        
        data = api_call_with_retry(
            dhan.intraday_minute_data,
            security_id=stock['security_id'],
            exchange_segment=stock['exchange_segment'],
            instrument_type=stock['instrument_type'],
            interval=interval,
            from_date=from_dt.strftime('%Y-%m-%d %H:%M:%S'),
            to_date=to_dt.strftime('%Y-%m-%d %H:%M:%S')
        )
        
        if data['status'] == 'success':
            df = pd.DataFrame(data['data'])
            if not df.empty:
                # Convert UTC timestamps to IST (Indian Standard Time)
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s', utc=True)
                df['timestamp'] = df['timestamp'].dt.tz_convert('Asia/Kolkata')
                # Remove timezone info for consistency with existing code
                df['timestamp'] = df['timestamp'].dt.tz_localize(None)
                return df
        else:
            log_event(stock['name'], timeframe, 'API_ERROR', data.get('message', 'No message'))
            
    except Exception as e:
        log_event(stock['name'], timeframe, 'EXCEPTION', str(e))
    
    return pd.DataFrame()

def fetch_daily_data(stock, from_date, to_date):
    """Fetch daily historical data"""
    try:
        data = api_call_with_retry(
            dhan.historical_daily_data,
            security_id=stock['security_id'],
            exchange_segment=stock['exchange_segment'],
            instrument_type=stock['instrument_type'],
            expiry_code=0,
            from_date=from_date.strftime('%Y-%m-%d'),
            to_date=to_date.strftime('%Y-%m-%d')
        )
        
        if data['status'] == 'success':
            df = pd.DataFrame(data['data'])
            if not df.empty:
                # Convert UTC timestamps to IST (Indian Standard Time)
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s', utc=True)
                df['timestamp'] = df['timestamp'].dt.tz_convert('Asia/Kolkata')
                # Remove timezone info for consistency with existing code
                df['timestamp'] = df['timestamp'].dt.tz_localize(None)
                return df
        else:
            log_event(stock['name'], 'daily', 'API_ERROR', data.get('message', 'No message'))
            
    except Exception as e:
        log_event(stock['name'], 'daily', 'EXCEPTION', str(e))
    
    return pd.DataFrame()

# ============================================================================
# DATA MANAGEMENT FUNCTIONS
# ============================================================================

def save_live_quotes():
    """Save live market quotes to CSV"""
    live_quotes = fetch_live_market_quotes()

    if live_quotes:
        quote_data = []
        timestamp = get_current_time()

        for exchange, instruments in live_quotes.items():
            for security_id, quote in instruments.items():
                # Find stock name from our stocks list
                stock_name = next((s['name'] for s in STOCKS if s['security_id'] == security_id), security_id)

                quote_data.append({
                    'timestamp': timestamp,
                    'stock': stock_name,
                    'security_id': security_id,
                    'exchange': exchange,
                    'ltp': quote.get('LTP', 0),
                    'open': quote.get('open', 0),
                    'high': quote.get('high', 0),
                    'low': quote.get('low', 0),
                    'close': quote.get('close', 0),
                    'volume': quote.get('volume', 0)
                })

        if quote_data:
            df = pd.DataFrame(quote_data)
            live_file = 'stock_data/live_quotes.csv'

            # Append to existing file or create new one
            if os.path.exists(live_file):
                df.to_csv(live_file, mode='a', header=False, index=False)
            else:
                df.to_csv(live_file, mode='w', header=True, index=False)

            log_event('LIVE_QUOTES', 'all', 'UPDATE', f"Live quotes saved for {len(quote_data)} instruments")
            return True

    return False

def update_historical_data(stock, timeframe):
    """Update historical data for a stock and timeframe"""
    filename = get_filename(stock, timeframe)
    today = get_today()

    if timeframe == 'daily':
        # For daily data, keep last 3 days (rolling window)
        trading_days = get_trading_days(DAILY_DATA_DAYS)
        from_date = trading_days[0]
        to_date = trading_days[-1]

        # Fetch daily data
        df = fetch_daily_data(stock, from_date, to_date)

        if not df.empty:
            # Save daily data (replace entire file to maintain rolling window)
            df.to_csv(filename, index=False)
            log_event(stock['name'], 'daily', 'UPDATE', f"Updated daily data: {len(df)} records")

    else:
        # For intraday data, manage last 5 trading days
        trading_days = get_trading_days(HISTORICAL_DAYS)

        # Check existing data
        if os.path.exists(filename):
            existing_df = pd.read_csv(filename)
            if not existing_df.empty:
                # Timestamps in CSV are already in IST format
                existing_df['timestamp'] = pd.to_datetime(existing_df['timestamp'])
                latest_time = existing_df['timestamp'].max()

                # If we have today's data up to current time, skip
                if latest_time.date() >= today and is_market_open():
                    # During market hours, fetch from last timestamp to now
                    from_dt = latest_time + datetime.timedelta(minutes=TIMEFRAMES[timeframe])
                    to_dt = get_current_time()
                else:
                    # After market hours or no recent data, fetch full day
                    from_dt = datetime.datetime.combine(today, MARKET_OPEN)
                    to_dt = get_current_time()
            else:
                # Empty file, fetch from market open
                from_dt = datetime.datetime.combine(today, MARKET_OPEN)
                to_dt = get_current_time()
        else:
            # No file exists, fetch from market open
            from_dt = datetime.datetime.combine(today, MARKET_OPEN)
            to_dt = get_current_time()

        # Fetch new data
        new_df = fetch_intraday_data(stock, timeframe, from_dt, to_dt)

        if not new_df.empty:
            # Combine with existing data
            if os.path.exists(filename):
                existing_df = pd.read_csv(filename)
                if not existing_df.empty:
                    # Timestamps in CSV are already in IST format
                    existing_df['timestamp'] = pd.to_datetime(existing_df['timestamp'])
                    combined_df = pd.concat([existing_df, new_df], ignore_index=True)
                else:
                    combined_df = new_df
            else:
                combined_df = new_df

            # Remove duplicates and sort
            combined_df = combined_df.drop_duplicates(subset=['timestamp']).sort_values('timestamp')

            # Keep only last 5 trading days of data
            cutoff_date = trading_days[0]
            cutoff_datetime = datetime.datetime.combine(cutoff_date, datetime.time.min)
            combined_df = combined_df[combined_df['timestamp'] >= cutoff_datetime]

            # Save updated data
            combined_df.to_csv(filename, index=False)
            log_event(stock['name'], timeframe, 'UPDATE', f"Updated {timeframe} data: {len(new_df)} new records")

def cleanup_old_data():
    """Clean up data older than retention period"""
    trading_days = get_trading_days(HISTORICAL_DAYS)
    cutoff_date = trading_days[0]

    for stock in STOCKS:
        for timeframe in TIMEFRAMES:
            if timeframe == 'daily':
                continue  # Daily data is managed separately

            filename = get_filename(stock, timeframe)
            if os.path.exists(filename):
                df = pd.read_csv(filename)
                if not df.empty:
                    # Timestamps in CSV are already in IST format
                    df['timestamp'] = pd.to_datetime(df['timestamp'])
                    cutoff_datetime = datetime.datetime.combine(cutoff_date, datetime.time.min)

                    # Keep only data from last 5 trading days
                    filtered_df = df[df['timestamp'] >= cutoff_datetime]

                    if len(filtered_df) < len(df):
                        filtered_df.to_csv(filename, index=False)
                        removed_count = len(df) - len(filtered_df)
                        log_event(stock['name'], timeframe, 'CLEANUP', f"Removed {removed_count} old records")

# ============================================================================
# MAIN EXECUTION FUNCTIONS
# ============================================================================

def run_live_data_collection():
    """Run live data collection during market hours"""
    print("🚀 Starting live data collection...")
    log_event('SYSTEM', 'live', 'START', 'Live data collection started')

    last_live_fetch = get_current_time() - datetime.timedelta(seconds=LIVE_DATA_INTERVAL)
    last_intraday_update = get_current_time() - datetime.timedelta(seconds=INTRADAY_UPDATE_INTERVAL)

    while is_market_open() and is_trading_day(get_today()):
        current_time = get_current_time()

        # Fetch live quotes at regular intervals
        if (current_time - last_live_fetch).total_seconds() >= LIVE_DATA_INTERVAL:
            print(f"📊 Fetching live quotes at {current_time.strftime('%H:%M:%S')}")
            save_live_quotes()
            last_live_fetch = current_time

        # Update intraday data at regular intervals
        if (current_time - last_intraday_update).total_seconds() >= INTRADAY_UPDATE_INTERVAL:
            print(f"📈 Updating intraday data at {current_time.strftime('%H:%M:%S')}")
            for stock in STOCKS:
                for timeframe in TIMEFRAMES:
                    if timeframe != 'daily':
                        update_historical_data(stock, timeframe)
            last_intraday_update = current_time

        # Sleep for 10 seconds before next check
        time.sleep(10)

    log_event('SYSTEM', 'live', 'END', 'Live data collection ended')
    print("🔴 Live data collection ended (market closed)")

def run_historical_data_update():
    """Update historical data when market is closed"""
    print("📚 Updating historical data...")
    log_event('SYSTEM', 'historical', 'START', 'Historical data update started')

    for stock in STOCKS:
        print(f"Updating data for {stock['name']}...")

        # Update all timeframes
        for timeframe in TIMEFRAMES:
            update_historical_data(stock, timeframe)
            time.sleep(0.1)  # Small delay between requests

    # Cleanup old data
    cleanup_old_data()

    log_event('SYSTEM', 'historical', 'END', 'Historical data update completed')
    print("✅ Historical data update completed")

def main():
    """Main execution function"""
    print("=" * 60)
    print("🚀 DhanHQ Enhanced Live Data Fetcher")
    print("=" * 60)
    print(f"📅 Current Time: {get_current_time().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🕘 Market Hours: {MARKET_OPEN} - {MARKET_CLOSE}")
    print(f"📊 Stocks: {', '.join([s['name'] for s in STOCKS])}")
    print(f"⏱️  Timeframes: {', '.join(TIMEFRAMES.keys())}")
    print(f"📈 Historical Days: {HISTORICAL_DAYS} trading days")
    print(f"📊 Daily Data Days: {DAILY_DATA_DAYS} days")
    print("=" * 60)

    # Initial historical data update
    print("\n🔄 Performing initial historical data update...")
    run_historical_data_update()

    # Main loop - wait for market and collect live data
    while True:
        try:
            # Wait for market to open
            wait_for_market_open()

            # Market is open, start live data collection
            run_live_data_collection()

            # Market closed, update historical data
            print("\n🔄 Market closed. Updating historical data...")
            run_historical_data_update()

            # Wait a bit before checking market status again
            print("\n⏰ Waiting for next market session...")
            time.sleep(300)  # Wait 5 minutes before checking again

        except KeyboardInterrupt:
            print("\n\n🛑 Stopping data collection...")
            log_event('SYSTEM', 'main', 'STOP', 'Manual stop requested')
            break
        except Exception as e:
            print(f"\n❌ Error in main loop: {str(e)}")
            log_event('SYSTEM', 'main', 'ERROR', str(e))
            print("⏰ Waiting 60 seconds before retry...")
            time.sleep(60)

    print("👋 Data collection stopped.")

if __name__ == "__main__":
    main()
