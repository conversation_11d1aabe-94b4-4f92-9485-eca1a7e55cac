#!/usr/bin/env python3
"""
Script to generate WMA crossover indicators only (no supply/demand zones)
"""

import pandas as pd
import numpy as np
import os
import sys

# Import the functions from the main script
sys.path.append('.')
from multi_timeframe_live_fetcher import (
    calculate_wma_crossover_signals, 
    add_wma_crossover_columns,
    TIMEFRAMES
)

def process_timeframe_crossovers(timeframe):
    """Process historical data for WMA crossover signals only"""
    tf_name = TIMEFRAMES[timeframe]['name']
    historical_file = f'Historical_Data_Layer/historical_{tf_name}.csv'
    
    print(f"\n🔍 Processing {tf_name} timeframe for crossover signals...")
    
    # Check if historical file exists
    if not os.path.exists(historical_file):
        print(f"❌ Historical file not found: {historical_file}")
        return False
    
    # Read historical data
    df = pd.read_csv(historical_file)
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    
    print(f"📊 Loaded {len(df)} candles from {historical_file}")
    
    if len(df) < 10:
        print(f"⚠️ Not enough data for analysis (need at least 10 candles)")
        return False
    
    # Convert to list format for analysis
    data_list = [row.to_dict() for _, row in df.iterrows()]
    
    # Analyze for WMA crossover signals only
    print(f"🔍 Analyzing WMA crossover signals...")
    analysis_result = calculate_wma_crossover_signals(data_list)
    
    if analysis_result and analysis_result['signals']:
        print(f"📊 Found {len(analysis_result['signals'])} WMA crossover signals")
        
        # Log signal summary
        up_signals = [s for s in analysis_result['signals'] if s['signal'] == 'UP']
        down_signals = [s for s in analysis_result['signals'] if s['signal'] == 'DOWN']
        
        print(f"🔼 UP signals: {len(up_signals)}, 🔽 DOWN signals: {len(down_signals)}")
    else:
        print(f"ℹ️ No WMA crossover signals detected")
        analysis_result = {'signals': [], 'wma5': [], 'wma10': []}
    
    # Add WMA crossover column to historical data (remove any existing zone columns)
    print(f"📈 Adding WMA5_10 column only...")

    # Remove any existing zone-related columns
    if 'PriceActive' in df.columns:
        df = df.drop('PriceActive', axis=1)
        print(f"🗑️ Removed PriceActive column")

    enhanced_df = add_wma_crossover_columns(df.copy(), analysis_result)
    
    # Save enhanced historical CSV
    enhanced_df.to_csv(historical_file, index=False)
    print(f"✅ Enhanced historical CSV saved: {historical_file}")
    
    return True

def main():
    """Main function to process all timeframes for crossover signals only"""
    print("🚀 Starting WMA Crossover Signal Analysis (No Zones)...")
    
    # Process each timeframe
    for timeframe in TIMEFRAMES.keys():
        try:
            success = process_timeframe_crossovers(timeframe)
            if success:
                print(f"✅ Successfully processed {TIMEFRAMES[timeframe]['name']} timeframe")
            else:
                print(f"❌ Failed to process {TIMEFRAMES[timeframe]['name']} timeframe")
        except Exception as e:
            print(f"❌ Error processing {TIMEFRAMES[timeframe]['name']}: {str(e)}")
    
    print("\n🎉 Crossover signal analysis completed!")
    print("\n📊 CSV files now contain:")
    print("   - wma5_10 column: UP/DOWN/HOLD signals")
    print("   - No supply/demand zone data")

if __name__ == "__main__":
    main()
