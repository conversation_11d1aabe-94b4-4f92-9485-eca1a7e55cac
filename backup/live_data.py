import pdb
import time
import datetime
import traceback
from Dhan_Tradehull import Tradehull
import pandas as pd
from pprint import pprint



client_code = "1105577608"
token_id    = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJpc3MiOiJkaGFuIiwicGFydG5lcklkIjoiIiwiZXhwIjoxNzUwOTU2MDc2LCJ0b2tlbkNvbnN1bWVyVHlwZSI6IlNFTEYiLCJ3ZWJob29rVXJsIjoiIiwiZGhhbkNsaWVudElkIjoiMTEwNTU3NzYwOCJ9.suPPlPFFhOK_W4AumsLqIGMhF3Ez_rrFT4KF90Ndj3UruoRmOJ1AonS8BtFpYjWf4rP243mLO5HlWZqqn3XHDw"





tsl         = Tradehull(client_code,token_id)
chart       = tsl.get_historical_data(tradingsymbol = 'NIFTY',exchange = 'INDEX',timeframe="5")
chart = chart.set_index(chart['timestamp'])
chart = chart.drop(columns=['timestamp'])
# char["sys_timestamp"] = chart.index
chart.to_csv('nifty_5min.csv')
pdb.set_trace()





# pdb.set_trace()
# chart = tsl.get_historical_data(tradingsymbol = 'SENSEX 27 DEC 78500 CALL',exchange = 'BFO',timeframe="5")


# tsl.get_historical_data(tradingsymbol='SENSEX 27 DEC 78500 CALL', exchange='BFO', timeframe="5")

# ltp   = tsl.get_ltp_data(names = ['NIFTY DEC FUT'])


# chart.sort_values(by="close")


# new_chart = chart.set_index(chart['timestamp'])


# 5th   candle     iloc
# 10:35 candle      loc

# # Get current system time
# current_time = pd.Timestamp.now()
# print(f"Current time: {current_time}")

# # To update every minute in a loop
# while True:
#     current_time = pd.Timestamp.now()
#     print(f"Current time: {current_time}")
#     # Round to nearest minute for display
#     minute_time = current_time.floor('min')
#     print(f"Current minute: {minute_time}")
#     # Wait until next minute
#     time.sleep(60 - current_time.second)
