#!/usr/bin/env python3
"""
Script to generate supply/demand zones and add WMA indicators to existing historical data
"""

import pandas as pd
import numpy as np
import os
import sys

# Import the functions from the main script
sys.path.append('.')
from multi_timeframe_live_fetcher import (
    calculate_wma_signals, 
    append_zones_to_csv, 
    add_wma_and_zone_columns,
    TIMEFRAMES
)

def process_timeframe_data(timeframe):
    """Process historical data for a specific timeframe"""
    tf_name = TIMEFRAMES[timeframe]['name']
    historical_file = f'Historical_Data_Layer/historical_{tf_name}.csv'
    
    print(f"\n🔍 Processing {tf_name} timeframe...")
    
    # Check if historical file exists
    if not os.path.exists(historical_file):
        print(f"❌ Historical file not found: {historical_file}")
        return False
    
    # Read historical data
    df = pd.read_csv(historical_file)
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    
    print(f"📊 Loaded {len(df)} candles from {historical_file}")
    
    if len(df) < 10:
        print(f"⚠️ Not enough data for analysis (need at least 10 candles)")
        return False
    
    # Convert to list format for analysis
    data_list = [row.to_dict() for _, row in df.iterrows()]
    
    # Analyze for supply/demand zones
    print(f"🔍 Analyzing supply/demand zones...")
    analysis_result = calculate_wma_signals(data_list)
    
    if analysis_result and analysis_result['zones']:
        print(f"📊 Found {len(analysis_result['zones'])} supply/demand zones")
        append_zones_to_csv(timeframe, analysis_result['zones'])
        
        # Log zone summary
        supply_zones = [z for z in analysis_result['zones'] if z['zone_type'] == 'SUPPLY']
        demand_zones = [z for z in analysis_result['zones'] if z['zone_type'] == 'DEMAND']
        
        print(f"🔴 Supply zones: {len(supply_zones)}, 🟢 Demand zones: {len(demand_zones)}")
    else:
        print(f"ℹ️ No supply/demand zones detected")
        analysis_result = {'zones': [], 'wma5': [], 'wma10': []}
    
    # Add WMA and zone columns to historical data
    print(f"📈 Adding WMA5_10 and PriceActive columns...")
    enhanced_df = add_wma_and_zone_columns(df.copy(), analysis_result)
    
    # Save enhanced historical CSV
    enhanced_df.to_csv(historical_file, index=False)
    print(f"✅ Enhanced historical CSV saved: {historical_file}")
    
    return True

def main():
    """Main function to process all timeframes"""
    print("🚀 Starting Supply/Demand Zone Analysis and Indicator Generation...")
    
    # Process each timeframe
    for timeframe in TIMEFRAMES.keys():
        try:
            success = process_timeframe_data(timeframe)
            if success:
                print(f"✅ Successfully processed {TIMEFRAMES[timeframe]['name']} timeframe")
            else:
                print(f"❌ Failed to process {TIMEFRAMES[timeframe]['name']} timeframe")
        except Exception as e:
            print(f"❌ Error processing {TIMEFRAMES[timeframe]['name']}: {str(e)}")
    
    print("\n🎉 Zone analysis and indicator generation completed!")

if __name__ == "__main__":
    main()
