#!/usr/bin/env python3
"""
🧠 TEST SCRIPT FOR AGENTIC INDIAN MARKET LIVE TRADING ASSISTANT

This script demonstrates the enhanced capabilities of the trading assistant:
- Command-based queries for trading signals
- Zone analysis and continuation patterns
- WMA crossover detection and export
- Market session information

Usage Examples:
1. python test_agentic_assistant.py --first-crossover 5
2. python test_agentic_assistant.py --demand-zones 1
3. python test_agentic_assistant.py --export-wma 60
4. python test_agentic_assistant.py --zone-patterns 5
5. python test_agentic_assistant.py --market-status
"""

import sys
import os
import argparse
import logging
from datetime import datetime

# Add the Scripts directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the enhanced trading assistant functions
from multi_timeframe_live_fetcher import (
    get_first_wma_crossover_today,
    get_demand_zones_after_down_crossover,
    export_wma_values_with_crossovers,
    get_zone_continuation_patterns,
    get_market_session_info,
    get_current_time_ist,
    TIMEFRAMES
)

# Configure logging for test script
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def print_test_banner():
    """Print test script banner"""
    banner = """
    ╔══════════════════════════════════════════════════════════════════════════════╗
    ║           🧠 AGENTIC TRADING ASSISTANT - TEST & DEMONSTRATION SCRIPT          ║
    ║                        Enhanced Command Interface Testing                    ║
    ╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)

def test_market_status():
    """Test market status functionality"""
    logger.info("🕐 TESTING MARKET STATUS FUNCTIONALITY")
    logger.info("=" * 60)
    
    current_time = get_current_time_ist()
    market_info = get_market_session_info()
    
    logger.info(f"📅 Current Time (IST): {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info(f"📈 Market Status: {market_info['status']}")
    logger.info(f"🔓 Market Open: {'Yes' if market_info['is_open'] else 'No'}")
    
    if 'session_progress' in market_info:
        logger.info(f"📊 Session Progress: {market_info['session_progress']:.1f}%")
    
    if 'time_remaining' in market_info:
        logger.info(f"⏰ Time Remaining: {market_info['time_remaining']}")
    elif 'time_to_open' in market_info:
        logger.info(f"⏰ Time to Open: {market_info['time_to_open']}")

def test_first_crossover(timeframe):
    """Test first crossover signal retrieval"""
    logger.info(f"🎯 TESTING FIRST CROSSOVER SIGNAL FOR {TIMEFRAMES[timeframe]['name'].upper()}")
    logger.info("=" * 60)
    
    try:
        signal = get_first_wma_crossover_today(timeframe)
        if signal:
            logger.info("✅ First crossover signal found and displayed above")
        else:
            logger.info("ℹ️ No crossover signals found for today")
    except Exception as e:
        logger.error(f"❌ Error retrieving first crossover: {str(e)}")

def test_demand_zones(timeframe):
    """Test demand zones after DOWN crossover"""
    logger.info(f"🎯 TESTING DEMAND ZONES AFTER DOWN CROSSOVER FOR {TIMEFRAMES[timeframe]['name'].upper()}")
    logger.info("=" * 60)
    
    try:
        zones = get_demand_zones_after_down_crossover(timeframe)
        if zones:
            logger.info(f"✅ Found {len(zones)} demand zones after DOWN crossover")
        else:
            logger.info("ℹ️ No demand zones found after DOWN crossover")
    except Exception as e:
        logger.error(f"❌ Error retrieving demand zones: {str(e)}")

def test_wma_export(timeframe):
    """Test WMA values export"""
    logger.info(f"📊 TESTING WMA VALUES EXPORT FOR {TIMEFRAMES[timeframe]['name'].upper()}")
    logger.info("=" * 60)
    
    try:
        output_file = export_wma_values_with_crossovers(timeframe)
        if output_file:
            logger.info(f"✅ WMA data exported successfully to: {output_file}")
        else:
            logger.info("ℹ️ No WMA data available for export")
    except Exception as e:
        logger.error(f"❌ Error exporting WMA data: {str(e)}")

def test_zone_patterns(timeframe):
    """Test zone continuation patterns"""
    logger.info(f"🔍 TESTING ZONE CONTINUATION PATTERNS FOR {TIMEFRAMES[timeframe]['name'].upper()}")
    logger.info("=" * 60)
    
    try:
        patterns = get_zone_continuation_patterns(timeframe)
        if patterns:
            logger.info(f"✅ Found {len(patterns)} zone continuation patterns")
        else:
            logger.info("ℹ️ No zone continuation patterns found")
    except Exception as e:
        logger.error(f"❌ Error retrieving zone patterns: {str(e)}")

def main():
    """Main test function"""
    print_test_banner()
    
    parser = argparse.ArgumentParser(
        description="Test Agentic Indian Market Live Trading Assistant",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s --market-status                    # Show current market status
  %(prog)s --first-crossover 5               # First crossover for 5min timeframe
  %(prog)s --demand-zones 1                  # Demand zones for 1min timeframe
  %(prog)s --export-wma 60                   # Export WMA data for 60min timeframe
  %(prog)s --zone-patterns 5                 # Zone patterns for 5min timeframe
  %(prog)s --all-tests 1                     # Run all tests for 1min timeframe
        """
    )
    
    parser.add_argument('--market-status', action='store_true',
                       help='Show current market status and session info')
    parser.add_argument('--first-crossover', type=str, choices=['1', '5', '60'],
                       help='Get first WMA crossover signal for timeframe')
    parser.add_argument('--demand-zones', type=str, choices=['1', '5', '60'],
                       help='List demand zones after DOWN crossover')
    parser.add_argument('--export-wma', type=str, choices=['1', '5', '60'],
                       help='Export WMA values with crossover markers')
    parser.add_argument('--zone-patterns', type=str, choices=['1', '5', '60'],
                       help='Detect zone continuation patterns')
    parser.add_argument('--all-tests', type=str, choices=['1', '5', '60'],
                       help='Run all tests for specified timeframe')
    
    args = parser.parse_args()
    
    # If no arguments provided, show help
    if not any(vars(args).values()):
        parser.print_help()
        return
    
    logger.info("🚀 Starting Agentic Trading Assistant Tests...")
    
    # Test market status
    if args.market_status or args.all_tests:
        test_market_status()
        print()
    
    # Test first crossover
    if args.first_crossover:
        test_first_crossover(args.first_crossover)
        print()
    elif args.all_tests:
        test_first_crossover(args.all_tests)
        print()
    
    # Test demand zones
    if args.demand_zones:
        test_demand_zones(args.demand_zones)
        print()
    elif args.all_tests:
        test_demand_zones(args.all_tests)
        print()
    
    # Test WMA export
    if args.export_wma:
        test_wma_export(args.export_wma)
        print()
    elif args.all_tests:
        test_wma_export(args.all_tests)
        print()
    
    # Test zone patterns
    if args.zone_patterns:
        test_zone_patterns(args.zone_patterns)
        print()
    elif args.all_tests:
        test_zone_patterns(args.all_tests)
        print()
    
    logger.info("✅ All tests completed!")

if __name__ == "__main__":
    main()
