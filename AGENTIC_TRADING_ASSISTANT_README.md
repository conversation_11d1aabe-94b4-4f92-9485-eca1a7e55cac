# 🧠 Agentic Indian Market Live Trading Assistant

## Enhanced Multi-Timeframe WMA Crossover & Supply/Demand Zone Detection System

### 🎯 Overview

This enhanced trading assistant provides real-time analysis of the Indian stock market (NSE) with sophisticated WMA crossover detection and supply/demand zone identification across multiple timeframes.

### 🔥 Key Features

#### 📊 **Start of Day Initialization**
- ✅ Load rolling candle buffers using historical CSVs
- ✅ Calculate WMAs from the very first live candle (e.g., 5WMA at 9:15 AM using 4 previous candles + live one)
- ✅ Backfill sufficient market days' data (1min=5days, 5min=10days, 60min=50days)
- ✅ Maintain indicator accuracy with enhanced buffer sizes

#### 🔥 **Live Market Processing (During NSE Trading Hours)**
- ✅ Fetch current candle at market-aligned timestamps (9:15-15:29 IST)
- ✅ Update deque and check for duplicates with enhanced validation
- ✅ Calculate WMA5, WMA10, and detect crossover events (UP, DOWN, HOLD)
- ✅ Detect and extend supply/demand zones based on crossover patterns
- ✅ Log new crossover and zone signals with precise timestamps and context

#### 📈 **Output Management**
- ✅ Write live candles and computed indicators to live.csv
- ✅ Periodically update historical.csv with latest in-memory deque
- ✅ Avoid duplicate rows/zones in both candles and zone logs
- ✅ Automatically append zones for next day using prepare_zones_for_tomorrow

#### 🎯 **Trading Conditions Enforced**
- ✅ Start calculating WMA only if required candles are available
- ✅ Ensure time-sensitive candle capture (aligned to 9:15, 9:16… etc.)
- ✅ Use exponential backoff retry logic on failed API calls
- ✅ Detect both first-time crossovers and zone continuation patterns

### 🚀 Quick Start

#### 1. **Run the Main Trading Assistant**
```bash
cd Scripts
python multi_timeframe_live_fetcher.py
```

#### 2. **Test Individual Features**
```bash
# Show current market status
python test_agentic_assistant.py --market-status

# Get first crossover signal for 5-min timeframe today
python test_agentic_assistant.py --first-crossover 5

# List all demand zones formed after DOWN crossover in 1-min chart
python test_agentic_assistant.py --demand-zones 1

# Export today's WMA5 and WMA10 values with crossover markers
python test_agentic_assistant.py --export-wma 60

# Detect zone continuation patterns from 60-min data
python test_agentic_assistant.py --zone-patterns 60

# Run all tests for 1-min timeframe
python test_agentic_assistant.py --all-tests 1
```

### 📊 Enhanced Timeframe Configurations

| Timeframe | Historical Days | Max Candles | Buffer Time | WMA Periods | Min Candles |
|-----------|----------------|-------------|-------------|-------------|-------------|
| **1min**  | 5 days         | 1,870       | 15 seconds  | 5WMA vs 10WMA | 10 |
| **5min**  | 10 days        | 750         | 30 seconds  | 5WMA vs 10WMA | 10 |
| **60min** | 50 days        | 350         | 120 seconds | 5WMA vs 10WMA | 10 |

### 🎯 Example Commands (As Mentioned in Your Prompt)

#### 1. **"Show me the first WMA crossover signal for the 5-min timeframe today."**
```python
from multi_timeframe_live_fetcher import get_first_wma_crossover_today
signal = get_first_wma_crossover_today('5')
```

#### 2. **"List all demand zones formed after a DOWN crossover in the 1-min chart."**
```python
from multi_timeframe_live_fetcher import get_demand_zones_after_down_crossover
zones = get_demand_zones_after_down_crossover('1')
```

#### 3. **"Export today's WMA5 and WMA10 values with crossover markers to CSV."**
```python
from multi_timeframe_live_fetcher import export_wma_values_with_crossovers
file_path = export_wma_values_with_crossovers('60')
```

#### 4. **"Detect zone continuation patterns from yesterday's 60-min data."**
```python
from multi_timeframe_live_fetcher import get_zone_continuation_patterns
patterns = get_zone_continuation_patterns('60')
```

#### 5. **"Log the exact time when the first 10WMA was valid today."**
The system automatically logs when WMA calculations become valid (after 10+ candles are available).

### 📁 Output Structure

```
├── Live_Data_Layer/                    # Live trading data with indicators
│   ├── NIFTY_INDEX_1min_live.csv
│   ├── NIFTY_INDEX_5min_live.csv
│   └── NIFTY_INDEX_60min_live.csv
├── Historical_Data_Layer/              # Historical data for backtesting
│   ├── historical_1min.csv
│   ├── historical_5min.csv
│   └── historical_60min.csv
├── Supply_Demand_Zones/                # Zone analysis results
│   ├── zones_1min.csv
│   ├── zones_5min.csv
│   └── zones_60min.csv
├── Trading_Signals_Log/                # Live trading signals
│   ├── signals_1min_YYYYMMDD.csv
│   ├── signals_5min_YYYYMMDD.csv
│   └── signals_60min_YYYYMMDD.csv
└── Tomorrow_Zone_Continuation/         # Next day zone preparation
    ├── zones_1min_YYYYMMDD.csv
    ├── zones_5min_YYYYMMDD.csv
    └── zones_60min_YYYYMMDD.csv
```

### 🔧 Technical Assumptions

- **Market Hours**: 9:15 AM - 3:29 PM IST (NSE)
- **API**: Tradehull/DhanHQ for candle data
- **Historical Storage**: CSV files for persistence and backtesting
- **WMA Calculation**: Uses ta (technical analysis) library
- **Zone Definition**: Between confirmed crossovers with metadata (strength, type, price range)

### 🚨 Live Trading Alerts

The system provides real-time alerts for:
- **WMA Crossover Signals**: UP/DOWN crossovers with precise timing
- **Supply/Demand Zone Creation**: New zones with strength indicators
- **Zone Continuation**: Multi-day zone tracking
- **Market Status Changes**: Open/close notifications

### 📊 Enhanced CSV Output Format

The live CSV files include comprehensive columns:
```csv
open,high,low,close,volume,timestamp,wma5,wma10,wma5_10,supply_zone,demand_zone,zone_level,zone_strength,PriceActive
```

### 🔄 Continuous Operation

The system runs continuously during market hours with:
- **Thread-safe API calls** with exponential backoff
- **Automatic retry logic** for failed requests
- **Real-time signal detection** with duplicate prevention
- **End-of-day zone preparation** for next trading session

### 🧠 Agentic Capabilities

- **Intelligent Initialization**: Automatically loads required historical data
- **Adaptive Signal Detection**: Validates timing and prevents duplicate alerts
- **Automated Zone Management**: Creates, tracks, and continues zones across days
- **Comprehensive Logging**: All signals and zones are logged for analysis

### 🎯 Next Steps

1. **Run the main assistant**: `python Scripts/multi_timeframe_live_fetcher.py`
2. **Test individual features**: Use the test script for specific queries
3. **Monitor live signals**: Check the Trading_Signals_Log folder
4. **Analyze zones**: Review Supply_Demand_Zones CSV files
5. **Plan next day**: Check Tomorrow_Zone_Continuation folder

This enhanced system provides a comprehensive foundation for Indian market live trading with sophisticated technical analysis capabilities.
