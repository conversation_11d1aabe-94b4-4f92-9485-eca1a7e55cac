INFO:2025-06-05 11:44:27,751:MainThread:<PERSON>han.py  started system
INFO:2025-06-05 11:44:27,752:MainThread:STARTED THE PROGRAM
INFO:2025-06-05 11:50:27,911:MainThread:<PERSON>han.py  started system
INFO:2025-06-05 11:50:27,911:MainThread:STARTED THE PROGRAM
INFO:2025-06-05 11:54:38,072:MainThread:Dhan.py  started system
INFO:2025-06-05 11:54:38,072:MainThread:STARTED THE PROGRAM
INFO:2025-06-05 12:02:45,089:MainThread:Dhan.py  started system
INFO:2025-06-05 12:02:45,089:MainThread:STARTED THE PROGRAM
INFO:2025-06-05 12:09:49,789:MainThread:Dhan.py  started system
INFO:2025-06-05 12:09:49,789:MainThread:STARTED THE PROGRAM
INFO:2025-06-05 12:29:32,076:MainThread:Dhan.py  started system
INFO:2025-06-05 12:29:32,076:MainThread:STARTED THE PROGRAM
INFO:2025-06-05 12:53:11,851:MainThread:Dhan.py  started system
INFO:2025-06-05 12:53:11,851:MainThread:STARTED THE PROGRAM
INFO:2025-06-05 13:00:12,707:MainThread:Dhan.py  started system
INFO:2025-06-05 13:00:12,707:MainThread:STARTED THE PROGRAM
INFO:2025-06-05 13:03:26,712:MainThread:Dhan.py  started system
INFO:2025-06-05 13:03:26,712:MainThread:STARTED THE PROGRAM
INFO:2025-06-05 13:05:59,609:MainThread:Dhan.py  started system
INFO:2025-06-05 13:05:59,609:MainThread:STARTED THE PROGRAM
INFO:2025-06-05 13:14:21,218:MainThread:Dhan.py  started system
INFO:2025-06-05 13:14:21,218:MainThread:STARTED THE PROGRAM
INFO:2025-06-05 13:22:12,434:MainThread:Dhan.py  started system
INFO:2025-06-05 13:22:12,434:MainThread:STARTED THE PROGRAM
INFO:2025-06-05 13:27:48,734:MainThread:Dhan.py  started system
INFO:2025-06-05 13:27:48,734:MainThread:STARTED THE PROGRAM
INFO:2025-06-05 13:30:35,863:MainThread:Dhan.py  started system
INFO:2025-06-05 13:30:35,864:MainThread:STARTED THE PROGRAM
ERROR:2025-06-05 13:33:07,342:MainThread:Exception in dhanhq>>intraday_minute_data: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
ERROR:2025-06-05 13:33:07,343:MainThread:Exception in Getting OHLC data as {'status': 'failure', 'remarks': "('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))", 'data': ''}
Traceback (most recent call last):
  File "/home/<USER>/Algo-trade/live-data-fetching/.venv/lib/python3.11/site-packages/Dhan_Tradehull/Dhan_Tradehull.py", line 464, in get_historical_data
    raise Exception(ohlc)
Exception: {'status': 'failure', 'remarks': "('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))", 'data': ''}
ERROR:2025-06-05 13:37:07,407:MainThread:Exception in dhanhq>>intraday_minute_data: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))
ERROR:2025-06-05 13:37:07,408:MainThread:Exception in Getting OHLC data as {'status': 'failure', 'remarks': "('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))", 'data': ''}
Traceback (most recent call last):
  File "/home/<USER>/Algo-trade/live-data-fetching/.venv/lib/python3.11/site-packages/Dhan_Tradehull/Dhan_Tradehull.py", line 464, in get_historical_data
    raise Exception(ohlc)
Exception: {'status': 'failure', 'remarks': "('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))", 'data': ''}
INFO:2025-06-05 15:39:03,492:MainThread:Dhan.py  started system
INFO:2025-06-05 15:39:03,492:MainThread:STARTED THE PROGRAM
