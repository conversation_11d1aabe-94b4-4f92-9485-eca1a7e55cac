#!/usr/bin/env python3
"""
🧪 TEST SCRIPT FOR SUPPLY/DEMAND ZONE DETECTION

This script tests the enhanced supply/demand zone detection algorithm:
1. Tests WMA calculation and crossover detection
2. Tests zone creation between crossovers
3. Validates CSV output format
4. Verifies zone persistence

Run this script to validate the implementation before using in production.
"""

import pandas as pd
import numpy as np
import os
import sys
from datetime import datetime, timedelta

# Add Scripts directory to path
sys.path.append('Scripts')

try:
    from multi_timeframe_live_fetcher import (
        calculate_wma,
        calculate_wma_signals,
        add_enhanced_wma_and_zone_columns,
        TIMEFRAMES
    )
    print("✅ Successfully imported enhanced functions")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

def create_test_data():
    """Create synthetic test data with clear crossover patterns"""
    print("📊 Creating synthetic test data...")
    
    # Create timestamps
    start_time = datetime(2025, 1, 1, 9, 15)
    timestamps = [start_time + timedelta(minutes=i) for i in range(50)]
    
    # Create price data with clear crossover patterns
    base_price = 25000
    prices = []
    
    # Pattern: Start low, trend up (DOWN to UP crossover), then trend down (UP to DOWN crossover)
    for i in range(50):
        if i < 15:
            # Downtrend - should create DOWN crossover around candle 10
            price = base_price - (i * 2) + np.random.normal(0, 1)
        elif i < 35:
            # Uptrend - should create UP crossover around candle 20
            price = base_price - 30 + ((i - 15) * 3) + np.random.normal(0, 1)
        else:
            # Downtrend again - should create DOWN crossover around candle 35
            price = base_price + 30 - ((i - 35) * 2) + np.random.normal(0, 1)
        
        prices.append(price)
    
    # Create OHLC data
    data = []
    for i, (timestamp, close) in enumerate(zip(timestamps, prices)):
        high = close + abs(np.random.normal(0, 2))
        low = close - abs(np.random.normal(0, 2))
        open_price = prices[i-1] if i > 0 else close
        volume = np.random.randint(1000, 5000)
        
        data.append({
            'timestamp': timestamp.strftime('%Y-%m-%d %H:%M:%S'),
            'open': round(open_price, 2),
            'high': round(high, 2),
            'low': round(low, 2),
            'close': round(close, 2),
            'volume': volume
        })
    
    print(f"✅ Created {len(data)} test candles")
    return data

def test_wma_calculation():
    """Test WMA calculation function"""
    print("\n🧪 Testing WMA calculation...")
    
    # Simple test data
    prices = np.array([100, 101, 102, 103, 104, 105, 106, 107, 108, 109])
    
    # Test 5-period WMA
    wma5 = calculate_wma(prices, 5)
    
    # Manual calculation for verification
    # For index 4 (5th element): (100*1 + 101*2 + 102*3 + 103*4 + 104*5) / (1+2+3+4+5)
    expected_wma5_at_4 = (100*1 + 101*2 + 102*3 + 103*4 + 104*5) / 15
    
    if abs(wma5[4] - expected_wma5_at_4) < 0.01:
        print("✅ WMA calculation test passed")
        return True
    else:
        print(f"❌ WMA calculation test failed: expected {expected_wma5_at_4}, got {wma5[4]}")
        return False

def test_crossover_detection():
    """Test crossover detection and zone creation"""
    print("\n🧪 Testing crossover detection and zone creation...")
    
    # Create test data
    test_data = create_test_data()
    
    # Run analysis
    analysis_result = calculate_wma_signals(test_data)
    
    if not analysis_result:
        print("❌ Analysis failed - no result returned")
        return False
    
    # Check signals
    signals = analysis_result['signals']
    zones = analysis_result['zones']
    
    print(f"📊 Found {len(signals)} crossover signals")
    print(f"🎯 Created {len(zones)} supply/demand zones")
    
    # Validate signals
    if len(signals) >= 2:
        print("✅ Crossover detection test passed")
        
        # Display signals
        for i, signal in enumerate(signals):
            print(f"   Signal {i+1}: {signal['signal']} at {signal['timestamp']} "
                  f"(WMA5: {signal['wma5']:.2f}, WMA10: {signal['wma10']:.2f})")
    else:
        print("❌ Crossover detection test failed - insufficient signals")
        return False
    
    # Validate zones
    if len(zones) >= 1:
        print("✅ Zone creation test passed")
        
        # Display zones
        for i, zone in enumerate(zones):
            print(f"   Zone {i+1}: {zone['zone_type']} at level {zone['zone_level']:.2f} "
                  f"({zone['start_signal']}→{zone['end_signal']}, {zone['candle_count']} candles)")
    else:
        print("❌ Zone creation test failed - no zones created")
        return False
    
    return True

def test_csv_output():
    """Test CSV output format"""
    print("\n🧪 Testing CSV output format...")
    
    # Create test DataFrame
    test_data = create_test_data()
    df = pd.DataFrame(test_data)
    df['timestamp'] = pd.to_datetime(df['timestamp'])
    
    # Add required columns
    required_columns = {
        'wma5': 0.0,
        'wma10': 0.0, 
        'wma5_10': 'HOLD',
        'supply_zone': '',
        'demand_zone': '',
        'zone_level': 0.0,
        'zone_strength': '',
        'PriceActive': ''
    }
    
    for col, default_val in required_columns.items():
        df[col] = default_val
    
    # Run analysis
    analysis_result = calculate_wma_signals(test_data)
    
    if analysis_result:
        # Apply enhanced columns
        enhanced_df = add_enhanced_wma_and_zone_columns(df.copy(), analysis_result)
        
        # Check if required columns exist and have data
        wma5_data = enhanced_df['wma5'].dropna()
        wma10_data = enhanced_df['wma10'].dropna()
        crossover_data = enhanced_df[enhanced_df['wma5_10'] != 'HOLD']
        zone_data = enhanced_df[enhanced_df['PriceActive'] != '']
        
        print(f"📊 WMA5 values: {len(wma5_data)} non-null entries")
        print(f"📊 WMA10 values: {len(wma10_data)} non-null entries")
        print(f"📊 Crossover signals: {len(crossover_data)} entries")
        print(f"🎯 Zone markers: {len(zone_data)} entries")
        
        if len(wma5_data) > 0 and len(wma10_data) > 0:
            print("✅ CSV output format test passed")
            
            # Save test output
            test_output_file = "test_enhanced_output.csv"
            enhanced_df.to_csv(test_output_file, index=False)
            print(f"💾 Test output saved to: {test_output_file}")
            
            return True
        else:
            print("❌ CSV output format test failed - missing WMA data")
            return False
    else:
        print("❌ CSV output format test failed - analysis failed")
        return False

def main():
    """Run all tests"""
    print("🧪 SUPPLY/DEMAND ZONE DETECTION TEST SUITE")
    print("=" * 60)
    
    tests = [
        ("WMA Calculation", test_wma_calculation),
        ("Crossover Detection & Zone Creation", test_crossover_detection),
        ("CSV Output Format", test_csv_output)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"🧪 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Implementation is ready for use.")
        print("\n📋 Next steps:")
        print("   1. Run: python Scripts/generate_crossover_indicators.py")
        print("   2. Run: python Scripts/multi_timeframe_live_fetcher.py")
        print("   3. Check output in Historical_Data_Layer/ and Supply_Demand_Zones/")
    else:
        print("⚠️ Some tests failed. Please review the implementation.")
    
    return passed == total

if __name__ == "__main__":
    main()
